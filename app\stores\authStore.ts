import { <PERSON>th<PERSON><PERSON>, Student, supabase, Teacher, UserRole } from '@/lib/supabase';
import {
    clearStoredSessionData,
    getStoredSessionData,
    storeSessionData
} from '@/utils/sessionUtils';
import { create } from 'zustand';

interface AuthState {
  user: AuthUser | null;
  isLoading: boolean;
  isInitialized: boolean;
  error: string | null;

  // Auth actions
  signIn: (email: string, password: string, role: UserRole) => Promise<void>;
  signOut: () => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;

  // Session management
  checkSession: () => Promise<void>;
  initializeAuth: () => Promise<void>;
  clearError: () => void;
}



export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isLoading: false,
  isInitialized: false,
  error: null,

  signIn: async (email: string, password: string, role: UserRole) => {
    set({ isLoading: true, error: null });
    try {
      // Sign in with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (authError) throw new Error(authError.message);
      if (!authData.user) throw new Error('No user returned from authentication');

      // Fetch user profile based on role
      let profile;
      if (role === 'student') {
        const { data: studentData, error: studentError } = await supabase
          .from('students')
          .select('*')
          .eq('user_id', authData.user.id)
          .single();

        if (studentError) throw new Error(studentError.message);
        profile = studentData;
      } else if (role === 'teacher') {
        const { data: teacherData, error: teacherError } = await supabase
          .from('teachers')
          .select('*')
          .eq('user_id', authData.user.id)
          .single();

        if (teacherError) throw new Error(teacherError.message);
        profile = teacherData;
      }

      // Create user object
      const user: AuthUser = {
        id: authData.user.id,
        email: authData.user.email!,
        role,
        profile,
      };

      // Store session data securely
      await storeSessionData(user);

      // Set the authenticated user with profile
      set({
        user,
        isLoading: false,
        isInitialized: true,
      });
    } catch (error: any) {
      // Provide user-friendly error messages
      let errorMessage = error.message;

      if (error.message.includes('Invalid login credentials')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      } else if (error.message.includes('Email not confirmed')) {
        errorMessage = 'Please check your email and confirm your account before signing in.';
      } else if (error.message.includes('Too many requests')) {
        errorMessage = 'Too many sign-in attempts. Please wait a moment and try again.';
      } else if (error.message.includes('User not found')) {
        errorMessage = 'No account found with this email address.';
      } else if (error.message.includes('network')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }

      console.error('Sign-in error:', error.message);
      set({ error: errorMessage, isLoading: false, isInitialized: true });

      // Clear any existing session data on sign-in failure
      await clearStoredSessionData();
    }
  },

  signOut: async () => {
    set({ isLoading: true });
    try {
      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) throw new Error(error.message);

      // Clear stored session data
      await clearStoredSessionData();

      set({ user: null, isLoading: false, isInitialized: true });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
      // Even if Supabase signout fails, clear local session
      await clearStoredSessionData();
      set({ user: null, isLoading: false, isInitialized: true });
    }
  },

  changePassword: async (currentPassword: string, newPassword: string) => {
    set({ isLoading: true, error: null });
    try {
      // First verify the current password by trying to sign in
      const { data: { user }, error: signInError } = await supabase.auth.signInWithPassword({
        email: get().user?.email || '',
        password: currentPassword,
      });

      if (signInError) throw new Error('Current password is incorrect');

      // Update the password
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (updateError) throw new Error(updateError.message);

      // Update password_changed flag in the appropriate table
      const currentUser = get().user;
      if (currentUser?.role === 'student') {
        await supabase
          .from('students')
          .update({ password_changed: true })
          .eq('user_id', currentUser.id);
      } else if (currentUser?.role === 'teacher') {
        await supabase
          .from('teachers')
          .update({ password_changed: true })
          .eq('user_id', currentUser.id);
      }

      // Update stored session data with new timestamp
      if (currentUser) {
        await storeSessionData(currentUser);
      }

      set({ isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
    }
  },

  checkSession: async () => {
    set({ isLoading: true, error: null });
    try {
      // Check Supabase session directly
      const { data: { session } } = await supabase.auth.getSession();

      if (session) {
        // Determine the user's role by checking both tables
        const { data: studentData } = await supabase
          .from('students')
          .select('*')
          .eq('user_id', session.user.id)
          .single();

        if (studentData) {
          const user: AuthUser = {
            id: session.user.id,
            email: session.user.email!,
            role: 'student',
            profile: studentData as Student,
          };

          // Store the new session data
          await storeSessionData(user);

          set({
            user,
            isLoading: false,
            isInitialized: true,
          });
          return;
        }

        const { data: teacherData } = await supabase
          .from('teachers')
          .select('*')
          .eq('user_id', session.user.id)
          .single();

        if (teacherData) {
          const user: AuthUser = {
            id: session.user.id,
            email: session.user.email!,
            role: 'teacher',
            profile: teacherData as Teacher,
          };

          // Store the new session data
          await storeSessionData(user);

          set({
            user,
            isLoading: false,
            isInitialized: true,
          });
          return;
        }
      }

      // No valid session found, clear any stored data
      await clearStoredSessionData();
      set({ user: null, isLoading: false, isInitialized: true });
    } catch (error: any) {
      console.error('Session check error:', error);
      // On error, clear stored session and set as not authenticated
      await clearStoredSessionData();
      set({ error: error.message, isLoading: false, user: null, isInitialized: true });
    }
  },

  initializeAuth: async () => {
    set({ isLoading: true, error: null });
    try {
      // First check for stored session data
      const storedUser = await getStoredSessionData();

      if (storedUser) {
        // Set user immediately for fast startup
        set({
          user: storedUser,
          isLoading: false,
          isInitialized: true,
        });

        // Verify session in background
        setTimeout(async () => {
          try {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
              // Session expired, sign out
              console.log('Session expired during background check');
              await get().signOut();
            }
          } catch (error) {
            console.error('Background session verification failed:', error);
          }
        }, 1000);
      } else {
        // No stored session, check with Supabase directly
        await get().checkSession();
      }
    } catch (error: any) {
      console.error('Auth initialization error:', error);
      await clearStoredSessionData();
      set({ error: error.message, isLoading: false, user: null, isInitialized: true });
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));
