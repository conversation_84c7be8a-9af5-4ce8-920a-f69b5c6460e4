'use client'

import React, { useState, useEffect } from 'react'
import { createClient } from '@/utils/supabase/client'
import { AttendanceWithDetails, Student, AttendanceStatus } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calendar, CheckCircle, XCircle, Clock, Shield, User, TrendingUp, TrendingDown } from 'lucide-react'
import { cn } from '@/lib/utils'

interface StudentAttendanceHistoryProps {
  studentId?: string
}

const statusConfig = {
  present: { 
    label: 'Present', 
    color: 'text-green-600', 
    bgColor: 'bg-green-100', 
    icon: CheckCircle, 
    badgeVariant: 'default' as const 
  },
  absent: { 
    label: 'Absent', 
    color: 'text-red-600', 
    bgColor: 'bg-red-100', 
    icon: XCircle, 
    badgeVariant: 'destructive' as const 
  },
  late: { 
    label: 'Late', 
    color: 'text-yellow-600', 
    bgColor: 'bg-yellow-100', 
    icon: Clock, 
    badgeVariant: 'secondary' as const 
  },
  excused: { 
    label: 'Excused', 
    color: 'text-blue-600', 
    bgColor: 'bg-blue-100', 
    icon: Shield, 
    badgeVariant: 'outline' as const 
  }
}

export default function StudentAttendanceHistory({ studentId }: StudentAttendanceHistoryProps) {
  const [students, setStudents] = useState<Student[]>([])
  const [selectedStudent, setSelectedStudent] = useState<string>(studentId || '')
  const [attendanceHistory, setAttendanceHistory] = useState<AttendanceWithDetails[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [dateRange, setDateRange] = useState<string>('30')

  useEffect(() => {
    fetchStudents()
  }, [])

  useEffect(() => {
    if (selectedStudent) {
      fetchAttendanceHistory(selectedStudent)
    }
  }, [selectedStudent, dateRange])

  const fetchStudents = async () => {
    try {
      const supabase = createClient()

      // Get current user (admin)
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      if (authError || !user) {
        throw new Error('User not authenticated')
      }

      // Get admin's school
      const { data: adminData, error: adminError } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      if (adminError || !adminData) {
        throw new Error('Admin school not found')
      }

      const { data, error } = await supabase
        .from('students')
        .select('id, name, student_id, email, grade')
        .eq('school_id', adminData.school_id)
        .eq('role', 'student')
        .order('name')

      if (error) throw error

      setStudents(data || [])

      // If no studentId prop provided, select first student
      if (!studentId && data && data.length > 0) {
        setSelectedStudent(data[0].id)
      }
    } catch (error) {
      console.error('Error fetching students:', error)
      setError('Failed to fetch students')
    }
  }

  const fetchAttendanceHistory = async (studentId: string) => {
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const endDate = new Date().toISOString().split('T')[0]
      const startDate = new Date(Date.now() - parseInt(dateRange) * 24 * 60 * 60 * 1000)
        .toISOString().split('T')[0]

      const { data, error } = await supabase
        .from('attendance')
        .select(`
          *,
          class:classes(
            id,
            class_name,
            grade,
            section,
            subject
          )
        `)
        .eq('student_id', studentId)
        .gte('attendance_date', startDate)
        .lte('attendance_date', endDate)
        .order('attendance_date', { ascending: false })

      if (error) throw error

      const transformedData: AttendanceWithDetails[] = (data || []).map(record => ({
        ...record,
        student: { id: '', name: '', student_id: '' }, // Will be populated if needed
        class: record.class,
        marked_by_user: { id: record.marked_by, name: 'Unknown' }
      }))

      setAttendanceHistory(transformedData)
    } catch (error) {
      console.error('Error fetching attendance history:', error)
      setError('Failed to fetch attendance history')
    } finally {
      setLoading(false)
    }
  }

  const getAttendanceStats = () => {
    const total = attendanceHistory.length
    if (total === 0) return null

    const present = attendanceHistory.filter(record => record.status === 'present').length
    const absent = attendanceHistory.filter(record => record.status === 'absent').length
    const late = attendanceHistory.filter(record => record.status === 'late').length
    const excused = attendanceHistory.filter(record => record.status === 'excused').length

    const attendanceRate = (present / total) * 100

    return {
      total,
      present,
      absent,
      late,
      excused,
      attendanceRate
    }
  }

  const selectedStudentData = students.find(s => s.id === selectedStudent)
  const stats = getAttendanceStats()

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-destructive/15 border border-destructive/20 rounded-lg p-4">
          <p className="text-sm text-destructive">{error}</p>
        </div>
      )}

      {/* Student Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Student Attendance History
          </CardTitle>
          <CardDescription>
            View detailed attendance records for individual students
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Select Student</label>
              <Select value={selectedStudent} onValueChange={setSelectedStudent}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a student" />
                </SelectTrigger>
                <SelectContent>
                  {students.map(student => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.name} ({student.student_id}) - Grade {student.grade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Time Period</label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 3 months</SelectItem>
                  <SelectItem value="180">Last 6 months</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Student Info and Stats */}
      {selectedStudentData && stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Student Information */}
          <Card>
            <CardHeader>
              <CardTitle>Student Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Name:</span>
                <span className="text-sm">{selectedStudentData.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Student ID:</span>
                <span className="text-sm">{selectedStudentData.student_id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Grade:</span>
                <span className="text-sm">{selectedStudentData.grade}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Email:</span>
                <span className="text-sm">{selectedStudentData.email}</span>
              </div>
            </CardContent>
          </Card>

          {/* Attendance Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Attendance Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {stats.attendanceRate.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">Overall Attendance Rate</div>
                <div className="flex items-center justify-center mt-2">
                  {stats.attendanceRate >= 90 ? (
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                  )}
                  <span className={cn(
                    "text-xs",
                    stats.attendanceRate >= 90 ? "text-green-600" : "text-red-600"
                  )}>
                    {stats.attendanceRate >= 90 ? "Excellent" : "Needs Improvement"}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {Object.entries(statusConfig).map(([status, config]) => {
                  const count = stats[status as keyof typeof stats] as number
                  const Icon = config.icon
                  return (
                    <div key={status} className="text-center">
                      <div className="text-xl font-bold">{count}</div>
                      <Badge variant={config.badgeVariant} className="text-xs">
                        <Icon className="h-3 w-3 mr-1" />
                        {config.label}
                      </Badge>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Attendance History */}
      {selectedStudent && (
        <Card>
          <CardHeader>
            <CardTitle>Attendance Records</CardTitle>
            <CardDescription>
              Detailed attendance history for the selected period
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : attendanceHistory.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Calendar className="h-8 w-8 mx-auto mb-2" />
                <p>No attendance records found for the selected period</p>
              </div>
            ) : (
              <div className="space-y-3">
                {attendanceHistory.map(record => {
                  const config = statusConfig[record.status]
                  const Icon = config.icon
                  const date = new Date(record.attendance_date).toLocaleDateString()

                  return (
                    <div key={record.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className={cn(
                          "h-10 w-10 rounded-full flex items-center justify-center",
                          config.bgColor
                        )}>
                          <Icon className={cn("h-5 w-5", config.color)} />
                        </div>
                        <div>
                          <div className="font-medium">{date}</div>
                          <div className="text-sm text-muted-foreground">
                            {record.class.class_name} - {record.class.subject}
                          </div>
                        </div>
                      </div>

                      <div className="text-right">
                        <Badge variant={config.badgeVariant}>
                          {config.label}
                        </Badge>
                        {record.notes && (
                          <div className="text-xs text-muted-foreground mt-1 max-w-48 truncate">
                            {record.notes}
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground mt-1">
                          {new Date(record.marked_at).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
