import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'
import { verifyPassword } from '@/lib/password-utils'

export async function POST(request: NextRequest) {
  try {
    const { email, password, studentId } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    // First, try to authenticate with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (authError) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Get student record to check if password has been changed
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select('id, student_id, name, password_changed, temporary_password_hash')
      .eq('user_id', authData.user.id)
      .eq('role', 'student')
      .single()

    if (studentError || !student) {
      return NextResponse.json(
        { error: 'Student record not found' },
        { status: 404 }
      )
    }

    // If studentId is provided, verify it matches
    if (studentId && student.student_id !== studentId) {
      return NextResponse.json(
        { error: 'Student ID does not match' },
        { status: 401 }
      )
    }

    return NextResponse.json({
      success: true,
      student: {
        id: student.id,
        studentId: student.student_id,
        name: student.name,
        email: authData.user.email,
        passwordChanged: student.password_changed
      },
      requiresPasswordChange: !student.password_changed
    })

  } catch (error: any) {
    console.error('Student auth error:', error)
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    )
  }
}

// Password change endpoint for students
export async function PATCH(request: NextRequest) {
  try {
    const { currentPassword, newPassword, studentId } = await request.json()

    if (!currentPassword || !newPassword || !studentId) {
      return NextResponse.json(
        { error: 'Current password, new password, and student ID are required' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Verify current password by attempting to sign in
    const { error: verifyError } = await supabase.auth.signInWithPassword({
      email: user.email!,
      password: currentPassword
    })

    if (verifyError) {
      return NextResponse.json(
        { error: 'Current password is incorrect' },
        { status: 401 }
      )
    }

    // Update password in Supabase Auth
    const { error: updateError } = await supabase.auth.updateUser({
      password: newPassword
    })

    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update password' },
        { status: 500 }
      )
    }

    // Mark password as changed in students table
    const { error: dbUpdateError } = await supabase
      .from('students')
      .update({ 
        password_changed: true,
        temporary_password_hash: null // Clear the temporary password hash
      })
      .eq('user_id', user.id)
      .eq('student_id', studentId)

    if (dbUpdateError) {
      console.error('Failed to update student record:', dbUpdateError)
      // Don't fail the request, password was still updated in auth
    }

    return NextResponse.json({
      success: true,
      message: 'Password updated successfully'
    })

  } catch (error: any) {
    console.error('Password change error:', error)
    return NextResponse.json(
      { error: 'Failed to change password' },
      { status: 500 }
    )
  }
}
