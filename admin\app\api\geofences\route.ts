import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return NextResponse.json({ error: 'School not found' }, { status: 404 })
    }

    // Get all geofences for the school
    const { data: geofences, error } = await supabase
      .from('school_geofences')
      .select('*')
      .eq('school_id', schoolAdmin.school_id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching geofences:', error)
      return NextResponse.json({ error: 'Failed to fetch geofences' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: geofences || []
    })

  } catch (error) {
    console.error('Error in geofences GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return NextResponse.json({ error: 'School not found' }, { status: 404 })
    }

    const body = await request.json()
    const {
      name,
      center_latitude,
      center_longitude,
      radius_meters,
      allowed_check_in_start,
      allowed_check_in_end,
      allowed_check_out_start,
      allowed_check_out_end,
      allowed_days,
      is_active = true
    } = body

    // Validation
    if (!name || !center_latitude || !center_longitude || !radius_meters) {
      return NextResponse.json({
        error: 'Missing required fields: name, center_latitude, center_longitude, radius_meters'
      }, { status: 400 })
    }

    // Validate coordinates
    if (center_latitude < -90 || center_latitude > 90) {
      return NextResponse.json({
        error: 'Invalid latitude. Must be between -90 and 90'
      }, { status: 400 })
    }

    if (center_longitude < -180 || center_longitude > 180) {
      return NextResponse.json({
        error: 'Invalid longitude. Must be between -180 and 180'
      }, { status: 400 })
    }

    // Validate radius
    if (radius_meters < 10 || radius_meters > 1000) {
      return NextResponse.json({
        error: 'Invalid radius. Must be between 10 and 1000 meters'
      }, { status: 400 })
    }

    // Create geofence
    const { data: geofence, error } = await supabase
      .from('school_geofences')
      .insert([{
        school_id: schoolAdmin.school_id,
        name,
        center_latitude: parseFloat(center_latitude),
        center_longitude: parseFloat(center_longitude),
        radius_meters: parseInt(radius_meters),
        allowed_check_in_start,
        allowed_check_in_end,
        allowed_check_out_start,
        allowed_check_out_end,
        allowed_days: allowed_days || [1, 2, 3, 4, 5],
        is_active,
        created_by: user.id
      }])
      .select()
      .single()

    if (error) {
      console.error('Error creating geofence:', error)
      return NextResponse.json({ error: 'Failed to create geofence' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: geofence,
      message: 'Geofence created successfully'
    })

  } catch (error) {
    console.error('Error in geofences POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
