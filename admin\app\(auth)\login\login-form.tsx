'use client'

import { useState, useTransition } from 'react'
import { login } from './actions'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle } from 'lucide-react'

export function LoginForm() {
  const [isPending, startTransition] = useTransition()
  const [error, setError] = useState('')
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })


  const handleSubmit = async (formData: FormData) => {
    setError('')
    startTransition(async () => {
      try {
        await login(formData)
      } catch (err: unknown) {
        console.error('Login form error:', err)
        if (err.message && err.message.includes('NEXT_REDIRECT')) {
          // This is expected - the login action redirects on success
          return
        }
        setError('An unexpected error occurred. Please try again.')
      }
    })
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form action={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
          Email Address
        </label>
        <Input
          id="email"
          name="email"
          type="email"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={handleInputChange}
          required
          disabled={isPending}
          className={isPending ? 'opacity-50' : ''}
        />
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
          Password
        </label>
        <Input
          id="password"
          name="password"
          type="password"
          placeholder="Enter your password"
          value={formData.password}
          onChange={handleInputChange}
          required
          disabled={isPending}
          className={isPending ? 'opacity-50' : ''}
        />
      </div>

      <Button
        type="submit"
        className="w-full"
        disabled={isPending || !formData.email || !formData.password}
      >
        {isPending ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Signing in...
          </>
        ) : (
          'Sign In'
        )}
      </Button>
      </form>
    </div>
  )
}
