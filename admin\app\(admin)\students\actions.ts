'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/utils/supabase/server'
import { createServiceClient } from '@/utils/supabase/service'
import { generateStudentPassword, generateStudentId, hashPassword } from '@/lib/password-utils'
import { validateStudentData } from '@/lib/security'

export interface StudentRegistrationData {
  name: string
  email: string
  grade: string
  classId?: string
  parentName: string
  parentMobile: string
  address: string
  dateOfBirth: string
}

export interface StudentRegistrationResult {
  success?: boolean
  error?: string
  studentId?: string
  temporaryPassword?: string
  message?: string
}

export async function registerStudent(formData: FormData): Promise<StudentRegistrationResult> {
  try {
    const supabase = await createClient()
    
    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return { error: 'Unauthorized. Please log in as an admin.' }
    }

    // Get admin's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return { error: 'Admin school not found. Please contact support.' }
    }

    // Extract form data
    const studentData: StudentRegistrationData = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      grade: formData.get('grade') as string,
      parentName: formData.get('parentName') as string,
      parentMobile: formData.get('parentMobile') as string,
      address: formData.get('address') as string,
      dateOfBirth: formData.get('dateOfBirth') as string,
    }

    // Validate student data
    const validation = validateStudentData(studentData)
    if (!validation.isValid) {
      const errorMessage = Object.values(validation.errors)[0]
      return { error: errorMessage }
    }

    // Generate student credentials
    const temporaryPassword = generateStudentPassword(studentData.name, studentData.dateOfBirth)
    const studentId = generateStudentId(studentData.name)
    
    console.log('Generated credentials:', { studentId, temporaryPassword: temporaryPassword.substring(0, 3) + '***' })

    // Create student user account using service client (has admin privileges)
    const serviceSupabase = createServiceClient()
    const { data: authData, error: authCreateError } = await serviceSupabase.auth.admin.createUser({
      email: studentData.email,
      password: temporaryPassword,
      email_confirm: true, // Auto-confirm email for students
      user_metadata: {
        name: studentData.name,
        role: 'student',
        student_id: studentId,
        school_id: schoolAdmin.school_id
      }
    })

    if (authCreateError) {
      console.error('Auth creation error:', authCreateError)
      return { error: `Failed to create student account: ${authCreateError.message}` }
    }

    if (!authData.user) {
      return { error: 'Failed to create student account. No user data returned.' }
    }

    // Hash the password for storage in our database
    const hashedPassword = await hashPassword(temporaryPassword)

    // Insert student record into students table
    const { error: insertError } = await supabase
      .from('students')
      .insert([{
        user_id: authData.user.id,
        student_id: studentId,
        name: validation.data.name,
        email: validation.data.email,
        grade: validation.data.grade,
        parent_name: validation.data.parentName,
        parent_mobile: validation.data.parentMobile,
        address: validation.data.address,
        date_of_birth: validation.data.dateOfBirth,
        school_id: schoolAdmin.school_id,
        role: 'student',
        temporary_password_hash: hashedPassword,
        password_changed: false,
        created_by: user.id
      }])

    if (insertError) {
      console.error('Database insert error:', insertError)

      // Clean up the auth user if database insert fails
      try {
        await supabase.auth.admin.deleteUser(authData.user.id)
      } catch (cleanupError) {
        console.error('Failed to cleanup auth user:', cleanupError)
      }

      return { error: `Failed to save student record: ${insertError.message}` }
    }

    // If classId is provided, enroll student in the class
    if (validation.data.classId) {
      const { error: enrollError } = await supabase
        .from('student_classes')
        .insert([{
          student_id: authData.user.id, // Using the auth user ID as student reference
          class_id: validation.data.classId,
          school_id: schoolAdmin.school_id,
          enrollment_date: new Date().toISOString().split('T')[0],
          is_active: true
        }])

      if (enrollError) {
        console.error('Error enrolling student in class:', enrollError)
        // Don't fail the entire operation, just log the error
        console.warn('Student created but class enrollment failed')
      }
    }

    // Revalidate the students page
    revalidatePath('/students')

    return {
      success: true,
      studentId,
      temporaryPassword,
      message: `Student ${studentData.name} has been successfully registered with ID: ${studentId}`
    }

  } catch (error: unknown) {
    console.error('Student registration error:', error)
    return { error: `Registration failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}` }
  }
}

export async function getStudents() {
  try {
    const supabase = await createClient()
    
    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    // Get admin's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      throw new Error('School not found')
    }

    // Fetch students for this school
    const { data: students, error: studentsError } = await supabase
      .from('students')
      .select(`
        id,
        student_id,
        name,
        email,
        grade,
        parent_name,
        parent_mobile,
        address,
        date_of_birth,
        password_changed,
        created_at
      `)
      .eq('school_id', schoolAdmin.school_id)
      .eq('role', 'student')
      .order('created_at', { ascending: false })

    if (studentsError) {
      throw new Error(studentsError.message)
    }

    return students || []

  } catch (error: unknown) {
    console.error('Error fetching students:', error)
    throw error
  }
}

export async function deleteStudent(studentId: string): Promise<{ success?: boolean; error?: string }> {
  try {
    const supabase = await createClient()

    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return { error: 'Unauthorized. Please log in as an admin.' }
    }

    // Get admin's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return { error: 'Admin school not found. Please contact support.' }
    }

    // Get student data to delete auth user and check related records
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select('user_id, student_id, name')
      .eq('id', studentId)
      .eq('school_id', schoolAdmin.school_id)
      .single()

    if (studentError || !student) {
      return { error: 'Student not found.' }
    }

    console.log(`Starting deletion process for student: ${student.name} (ID: ${student.student_id})`)

    // Step 1: Delete all student-class enrollments
    const { error: classEnrollmentError } = await supabase
      .from('student_classes')
      .delete()
      .eq('student_id', studentId)
      .eq('school_id', schoolAdmin.school_id)

    if (classEnrollmentError) {
      console.error('Error deleting student class enrollments:', classEnrollmentError)
      return { error: `Failed to remove student class enrollments: ${classEnrollmentError.message}` }
    }

    // Step 2: Delete attendance records for this student
    // Note: We delete attendance records for students since they're tied to the student
    const { error: attendanceDeleteError } = await supabase
      .from('attendance')
      .delete()
      .eq('student_id', studentId)

    if (attendanceDeleteError) {
      console.error('Error deleting student attendance records:', attendanceDeleteError)
      // Don't fail the deletion for this, just log it
    }

    // Step 3: Delete the student record
    const { error: deleteError } = await supabase
      .from('students')
      .delete()
      .eq('id', studentId)
      .eq('school_id', schoolAdmin.school_id)

    if (deleteError) {
      console.error('Error deleting student record:', deleteError)
      return { error: `Failed to delete student: ${deleteError.message}` }
    }

    // Step 4: Delete auth user if exists
    if (student.user_id) {
      try {
        const serviceSupabase = createServiceClient()
        await serviceSupabase.auth.admin.deleteUser(student.user_id)
        console.log(`Successfully deleted auth user for student: ${student.name}`)
      } catch (authDeleteError) {
        console.error('Error deleting student auth user:', authDeleteError)
        // Don't return error here as the student record is already deleted
        // The auth user deletion is not critical for the main operation
      }
    }

    console.log(`Successfully completed deletion process for student: ${student.name}`)

    // Revalidate the students page
    revalidatePath('/students')

    return { success: true }

  } catch (error) {
    console.error('Unexpected error in deleteStudent:', error)
    return {
      error: error instanceof Error ? error.message : 'An unexpected error occurred while deleting the student.'
    }
  }
}
