import { TeacherAttendanceSettings } from '@/lib/supabase';
import * as Application from 'expo-application';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

export interface SecurityValidationResult {
  isValid: boolean;
  violations: SecurityViolation[];
  riskScore: number; // 0-100, higher is more risky
  deviceFingerprint: DeviceFingerprint;
}

export interface SecurityViolation {
  type: 'time_window' | 'location' | 'device' | 'network' | 'behavioral' | 'rate_limit';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details?: any;
}

export interface DeviceFingerprint {
  deviceId: string;
  deviceType: string;
  osVersion: string;
  appVersion: string;
  isPhysicalDevice: boolean;
  deviceName?: string;
  isRooted?: boolean;
  hasVPN?: boolean;
  networkType?: string;
  ipAddress?: string;
  userAgent: string;
}

export interface AttendanceAttempt {
  teacherId: string;
  timestamp: Date;
  location?: {
    latitude: number;
    longitude: number;
    accuracy: number;
  };
  deviceFingerprint: DeviceFingerprint;
  success: boolean;
  violations: SecurityViolation[];
}

export class SecurityService {
  private static instance: SecurityService;
  private attemptHistory: Map<string, AttendanceAttempt[]> = new Map();
  private lockoutMap: Map<string, Date> = new Map();

  private constructor() {}

  public static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  /**
   * Generate comprehensive device fingerprint
   */
  public async generateDeviceFingerprint(): Promise<DeviceFingerprint> {
    const deviceId = await this.getDeviceId();

    return {
      deviceId,
      deviceType: Platform.OS,
      osVersion: Device.osVersion || 'unknown',
      appVersion: Application.nativeApplicationVersion || 'unknown',
      isPhysicalDevice: Device.isDevice,
      deviceName: Device.deviceName || undefined,
      isRooted: await this.detectRootedDevice(),
      hasVPN: false, // Simplified for now
      networkType: 'unknown', // Simplified for now
      ipAddress: undefined, // Simplified for now
      userAgent: this.generateUserAgent(),
    };
  }

  /**
   * Validate attendance attempt with comprehensive security checks
   */
  public async validateAttendanceAttempt(
    teacherId: string,
    settings: TeacherAttendanceSettings,
    location?: { latitude: number; longitude: number; accuracy: number }
  ): Promise<SecurityValidationResult> {
    const violations: SecurityViolation[] = [];
    let riskScore = 0;

    // Generate device fingerprint
    const deviceFingerprint = await this.generateDeviceFingerprint();

    // Check rate limiting
    const rateLimitViolation = this.checkRateLimit(teacherId, settings);
    if (rateLimitViolation) {
      violations.push(rateLimitViolation);
      riskScore += 30;
    }

    // Check time window
    const timeViolation = this.checkTimeWindow(settings);
    if (timeViolation) {
      violations.push(timeViolation);
      riskScore += 25;
    }

    // Check device security
    const deviceViolations = this.checkDeviceSecurity(deviceFingerprint);
    violations.push(...deviceViolations);
    riskScore += deviceViolations.length * 15;

    // Check network security
    const networkViolations = this.checkNetworkSecurity(deviceFingerprint);
    violations.push(...networkViolations);
    riskScore += networkViolations.length * 10;

    // Check behavioral patterns
    const behavioralViolations = await this.checkBehavioralPatterns(teacherId, deviceFingerprint, location);
    violations.push(...behavioralViolations);
    riskScore += behavioralViolations.length * 20;

    // Record attempt
    this.recordAttempt(teacherId, deviceFingerprint, location, violations.length === 0, violations);

    return {
      isValid: violations.filter(v => v.severity === 'high' || v.severity === 'critical').length === 0,
      violations,
      riskScore: Math.min(riskScore, 100),
      deviceFingerprint,
    };
  }

  /**
   * Check if teacher is within rate limit
   */
  private checkRateLimit(teacherId: string, settings: TeacherAttendanceSettings): SecurityViolation | null {
    const maxAttempts = settings.max_daily_attempts || 5;
    const lockoutDuration = settings.lockout_duration_minutes || 30;

    // Check if teacher is currently locked out
    const lockoutUntil = this.lockoutMap.get(teacherId);
    if (lockoutUntil && new Date() < lockoutUntil) {
      const remainingMinutes = Math.ceil((lockoutUntil.getTime() - Date.now()) / (1000 * 60));
      return {
        type: 'rate_limit',
        severity: 'high',
        message: `Account temporarily locked. Try again in ${remainingMinutes} minutes.`,
        details: { lockoutUntil, remainingMinutes }
      };
    }

    // Check daily attempts
    const attempts = this.attemptHistory.get(teacherId) || [];
    const today = new Date().toDateString();
    const todayAttempts = attempts.filter(a => a.timestamp.toDateString() === today);

    if (todayAttempts.length >= maxAttempts) {
      // Apply lockout
      const lockoutUntil = new Date(Date.now() + lockoutDuration * 60 * 1000);
      this.lockoutMap.set(teacherId, lockoutUntil);

      return {
        type: 'rate_limit',
        severity: 'critical',
        message: `Maximum daily attempts exceeded. Account locked for ${lockoutDuration} minutes.`,
        details: { attempts: todayAttempts.length, maxAttempts, lockoutDuration }
      };
    }

    return null;
  }

  /**
   * Check if current time is within allowed window
   */
  private checkTimeWindow(settings: TeacherAttendanceSettings): SecurityViolation | null {
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 8);
    const currentDay = now.getDay();

    // Check if it's a weekend (assuming school is Monday-Friday)
    if (currentDay === 0 || currentDay === 6) {
      return {
        type: 'time_window',
        severity: 'medium',
        message: 'Attendance marking is not allowed on weekends.',
        details: { currentDay, dayName: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][currentDay] }
      };
    }

    // Check time window (assuming check-in window for now)
    const earliestTime = settings.earliest_check_in || '06:00:00';
    const latestTime = settings.latest_check_in || '10:00:00';

    if (currentTime < earliestTime || currentTime > latestTime) {
      return {
        type: 'time_window',
        severity: 'high',
        message: `Attendance can only be marked between ${earliestTime} and ${latestTime}.`,
        details: { currentTime, earliestTime, latestTime }
      };
    }

    return null;
  }

  /**
   * Check device security
   */
  private checkDeviceSecurity(fingerprint: DeviceFingerprint): SecurityViolation[] {
    const violations: SecurityViolation[] = [];

    // Check if device is physical
    if (!fingerprint.isPhysicalDevice) {
      violations.push({
        type: 'device',
        severity: 'critical',
        message: 'Attendance marking is not allowed from emulators or simulators.',
        details: { isPhysicalDevice: fingerprint.isPhysicalDevice }
      });
    }

    // Check if device is rooted/jailbroken
    if (fingerprint.isRooted) {
      violations.push({
        type: 'device',
        severity: 'high',
        message: 'Attendance marking is not allowed from rooted or jailbroken devices.',
        details: { isRooted: fingerprint.isRooted }
      });
    }

    return violations;
  }

  /**
   * Check network security
   */
  private checkNetworkSecurity(fingerprint: DeviceFingerprint): SecurityViolation[] {
    const violations: SecurityViolation[] = [];

    // Check for VPN usage (simplified for now)
    if (fingerprint.hasVPN) {
      violations.push({
        type: 'network',
        severity: 'medium',
        message: 'VPN usage detected. Please disable VPN and try again.',
        details: { hasVPN: fingerprint.hasVPN }
      });
    }

    return violations;
  }

  /**
   * Check behavioral patterns
   */
  private async checkBehavioralPatterns(
    teacherId: string,
    fingerprint: DeviceFingerprint,
    location?: { latitude: number; longitude: number; accuracy: number }
  ): Promise<SecurityViolation[]> {
    const violations: SecurityViolation[] = [];
    const attempts = this.attemptHistory.get(teacherId) || [];

    // Check for device changes
    const recentAttempts = attempts.filter(a => 
      Date.now() - a.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000 // Last 7 days
    );

    const uniqueDevices = new Set(recentAttempts.map(a => a.deviceFingerprint.deviceId));
    if (uniqueDevices.size > 3) {
      violations.push({
        type: 'behavioral',
        severity: 'medium',
        message: 'Multiple devices detected in recent attempts.',
        details: { uniqueDevices: uniqueDevices.size, threshold: 3 }
      });
    }

    // Check for impossible travel (if location is provided)
    if (location && recentAttempts.length > 0) {
      const lastAttemptWithLocation = recentAttempts
        .filter(a => a.location)
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

      if (lastAttemptWithLocation && lastAttemptWithLocation.location) {
        const timeDiff = (Date.now() - lastAttemptWithLocation.timestamp.getTime()) / 1000; // seconds
        const distance = this.calculateDistance(
          location.latitude,
          location.longitude,
          lastAttemptWithLocation.location.latitude,
          lastAttemptWithLocation.location.longitude
        );

        // Check for impossible travel speed (> 100 m/s = 360 km/h)
        const speed = distance / timeDiff;
        if (speed > 100 && timeDiff < 3600) { // Only check if less than 1 hour apart
          violations.push({
            type: 'behavioral',
            severity: 'high',
            message: 'Impossible travel speed detected between attendance attempts.',
            details: { distance, timeDiff, speed, threshold: 100 }
          });
        }
      }
    }

    return violations;
  }

  /**
   * Record attendance attempt for behavioral analysis
   */
  private recordAttempt(
    teacherId: string,
    fingerprint: DeviceFingerprint,
    location?: { latitude: number; longitude: number; accuracy: number },
    success: boolean = false,
    violations: SecurityViolation[] = []
  ): void {
    const attempts = this.attemptHistory.get(teacherId) || [];
    
    attempts.push({
      teacherId,
      timestamp: new Date(),
      location,
      deviceFingerprint: fingerprint,
      success,
      violations,
    });

    // Keep only last 100 attempts per teacher
    if (attempts.length > 100) {
      attempts.splice(0, attempts.length - 100);
    }

    this.attemptHistory.set(teacherId, attempts);
  }

  /**
   * Get unique device identifier
   */
  private async getDeviceId(): Promise<string> {
    try {
      if (Platform.OS === 'android') {
        return await Application.getAndroidId() || 'unknown-android';
      } else {
        return await Application.getIosIdForVendorAsync() || 'unknown-ios';
      }
    } catch {
      return 'unknown-device';
    }
  }

  /**
   * Detect if device is rooted/jailbroken
   */
  private async detectRootedDevice(): Promise<boolean> {
    // This is a simplified check - in production, use a dedicated library like react-native-jail-monkey
    try {
      if (Platform.OS === 'android') {
        // Check for common root indicators
        return false; // Placeholder - implement actual root detection
      } else {
        // Check for jailbreak indicators
        return false; // Placeholder - implement actual jailbreak detection
      }
    } catch {
      return false;
    }
  }

  /**
   * Detect VPN usage (simplified version)
   */
  private detectVPN(): boolean {
    // This is a simplified check - in production, use more sophisticated VPN detection
    // For now, we'll return false as we don't have network info
    return false;
  }

  /**
   * Generate user agent string
   */
  private generateUserAgent(): string {
    const appVersion = Application.nativeApplicationVersion || '1.0.0';
    const osVersion = Device.osVersion || 'unknown';
    
    if (Platform.OS === 'android') {
      return `SchoolManagementApp/${appVersion} (Android ${osVersion})`;
    } else {
      return `SchoolManagementApp/${appVersion} (iOS ${osVersion})`;
    }
  }

  /**
   * Calculate distance between two points (Haversine formula)
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Clear attempt history for a teacher (for testing purposes)
   */
  public clearAttemptHistory(teacherId: string): void {
    this.attemptHistory.delete(teacherId);
    this.lockoutMap.delete(teacherId);
  }

  /**
   * Get attempt statistics for a teacher
   */
  public getAttemptStatistics(teacherId: string): {
    totalAttempts: number;
    successfulAttempts: number;
    failedAttempts: number;
    todayAttempts: number;
    isLockedOut: boolean;
    lockoutUntil?: Date;
  } {
    const attempts = this.attemptHistory.get(teacherId) || [];
    const today = new Date().toDateString();
    const todayAttempts = attempts.filter(a => a.timestamp.toDateString() === today);
    const lockoutUntil = this.lockoutMap.get(teacherId);

    return {
      totalAttempts: attempts.length,
      successfulAttempts: attempts.filter(a => a.success).length,
      failedAttempts: attempts.filter(a => !a.success).length,
      todayAttempts: todayAttempts.length,
      isLockedOut: lockoutUntil ? new Date() < lockoutUntil : false,
      lockoutUntil,
    };
  }
}
