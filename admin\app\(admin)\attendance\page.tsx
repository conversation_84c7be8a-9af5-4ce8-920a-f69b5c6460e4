'use client'

import React, { useState } from 'react'
import EnhancedAttendanceMarking from '@/components/EnhancedAttendanceMarking'
import AttendanceDashboard from '@/components/AttendanceDashboard'
import StudentAttendanceHistory from '@/components/StudentAttendanceHistory'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { UserCheck, BarChart3, User } from 'lucide-react'

type TabType = 'marking' | 'dashboard' | 'history'

export default function AttendancePage() {
  const [activeTab, setActiveTab] = useState<TabType>('marking')

  const tabs = [
    {
      id: 'marking' as TabType,
      label: 'Mark Attendance',
      icon: UserCheck,
      description: 'Mark daily attendance for any class in your school'
    },
    {
      id: 'dashboard' as TabType,
      label: 'Analytics',
      icon: BarChart3,
      description: 'View attendance statistics and trends'
    },
    {
      id: 'history' as TabType,
      label: 'Student History',
      icon: User,
      description: 'View individual student attendance records'
    }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Attendance Management</h1>
        <p className="text-muted-foreground">
          Comprehensive attendance tracking and analytics system
        </p>
      </div>

      {/* Tab Navigation */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-2">
            {tabs.map(tab => {
              const Icon = tab.icon
              return (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? 'default' : 'outline'}
                  onClick={() => setActiveTab(tab.id)}
                  className="flex items-center gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </Button>
              )
            })}
          </div>
          <div className="mt-4 text-sm text-muted-foreground">
            {tabs.find(tab => tab.id === activeTab)?.description}
          </div>
        </CardContent>
      </Card>

      {/* Tab Content */}
      {activeTab === 'marking' && <EnhancedAttendanceMarking />}
      {activeTab === 'dashboard' && <AttendanceDashboard />}
      {activeTab === 'history' && <StudentAttendanceHistory />}
    </div>
  )
}
