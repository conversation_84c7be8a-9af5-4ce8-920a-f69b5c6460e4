{"expo": {"name": "school_management_app", "slug": "school_management_app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "schoolmanagementapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs access to your location to verify your attendance at school.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs access to your location to verify your attendance at school."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}