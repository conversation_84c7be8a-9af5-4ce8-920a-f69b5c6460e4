/**
 * Admin Configuration Service
 * 
 * This service handles fetching and caching admin-configured settings
 * from the web admin panel for use in the mobile app.
 */

import { supabase, SchoolGeofence, TeacherAttendanceSettings } from '@/lib/supabase';

export interface AdminConfig {
  geofences: SchoolGeofence[];
  settings: TeacherAttendanceSettings | null;
  lastUpdated: Date;
}

export class AdminConfigService {
  private static instance: AdminConfigService;
  private configCache: Map<string, AdminConfig> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  public static getInstance(): AdminConfigService {
    if (!AdminConfigService.instance) {
      AdminConfigService.instance = new AdminConfigService();
    }
    return AdminConfigService.instance;
  }

  /**
   * Fetch fresh configuration from admin panel
   */
  public async fetchAdminConfig(schoolId: string): Promise<AdminConfig> {
    try {
      console.log('Fetching admin configuration for school:', schoolId);

      // Fetch geofences
      const { data: geofences, error: geofenceError } = await supabase
        .from('school_geofences')
        .select('*')
        .eq('school_id', schoolId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (geofenceError) {
        console.error('Error fetching geofences:', geofenceError);
        throw new Error(`Failed to fetch geofences: ${geofenceError.message}`);
      }

      // Fetch settings
      const { data: settings, error: settingsError } = await supabase
        .from('teacher_attendance_settings')
        .select('*')
        .eq('school_id', schoolId)
        .single();

      if (settingsError && settingsError.code !== 'PGRST116') {
        console.error('Error fetching settings:', settingsError);
        throw new Error(`Failed to fetch settings: ${settingsError.message}`);
      }

      const config: AdminConfig = {
        geofences: geofences || [],
        settings: settings || null,
        lastUpdated: new Date(),
      };

      // Cache the configuration
      this.configCache.set(schoolId, config);

      console.log('Admin configuration fetched successfully:', {
        geofences: config.geofences.length,
        hasSettings: !!config.settings,
      });

      return config;
    } catch (error) {
      console.error('Failed to fetch admin configuration:', error);
      throw error;
    }
  }

  /**
   * Get configuration with caching
   */
  public async getAdminConfig(schoolId: string, forceRefresh = false): Promise<AdminConfig> {
    const cached = this.configCache.get(schoolId);
    
    // Return cached if valid and not forcing refresh
    if (!forceRefresh && cached && this.isCacheValid(cached)) {
      console.log('Using cached admin configuration');
      return cached;
    }

    // Fetch fresh configuration
    return this.fetchAdminConfig(schoolId);
  }

  /**
   * Check if cached configuration is still valid
   */
  private isCacheValid(config: AdminConfig): boolean {
    const now = new Date();
    const age = now.getTime() - config.lastUpdated.getTime();
    return age < this.cacheTimeout;
  }

  /**
   * Validate if school has proper configuration
   */
  public validateSchoolConfig(config: AdminConfig): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // Check geofences
    if (!config.geofences || config.geofences.length === 0) {
      issues.push('No active geofences configured. Teachers cannot mark attendance without location boundaries.');
    } else {
      // Validate each geofence
      config.geofences.forEach((geofence, index) => {
        if (!geofence.center_latitude || !geofence.center_longitude) {
          issues.push(`Geofence "${geofence.name}" has invalid coordinates.`);
        }
        if (!geofence.radius_meters || geofence.radius_meters <= 0) {
          issues.push(`Geofence "${geofence.name}" has invalid radius.`);
        }
        if (!geofence.allowed_check_in_start || !geofence.allowed_check_in_end) {
          issues.push(`Geofence "${geofence.name}" has invalid check-in time window.`);
        }
      });
    }

    // Check settings
    if (!config.settings) {
      issues.push('Attendance settings not configured. Using default security settings.');
    } else {
      if (!config.settings.max_location_accuracy_meters || config.settings.max_location_accuracy_meters <= 0) {
        issues.push('Invalid location accuracy setting.');
      }
      if (!config.settings.max_daily_attempts || config.settings.max_daily_attempts <= 0) {
        issues.push('Invalid daily attempts limit.');
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
    };
  }

  /**
   * Get default settings if none configured
   */
  public getDefaultSettings(): TeacherAttendanceSettings {
    return {
      id: 'default',
      school_id: '',
      earliest_check_in: '06:00:00',
      latest_check_in: '10:00:00',
      earliest_check_out: '14:00:00',
      latest_check_out: '20:00:00',
      require_location: true,
      max_location_accuracy_meters: 50,
      geofence_buffer_meters: 100,
      max_daily_attempts: 5,
      lockout_duration_minutes: 30,
      require_photo_verification: false,
      allow_manual_override: true,
      notify_admin_on_suspicious: true,
      notify_teacher_on_location_fail: true,
      minimum_work_hours: 8.0,
      break_time_minutes: 60,
    };
  }

  /**
   * Clear cache for a specific school
   */
  public clearCache(schoolId?: string): void {
    if (schoolId) {
      this.configCache.delete(schoolId);
      console.log('Cleared cache for school:', schoolId);
    } else {
      this.configCache.clear();
      console.log('Cleared all admin configuration cache');
    }
  }

  /**
   * Get configuration status for debugging
   */
  public getConfigStatus(schoolId: string): {
    hasCached: boolean;
    cacheAge?: number;
    isValid?: boolean;
  } {
    const cached = this.configCache.get(schoolId);
    
    if (!cached) {
      return { hasCached: false };
    }

    const age = new Date().getTime() - cached.lastUpdated.getTime();
    
    return {
      hasCached: true,
      cacheAge: age,
      isValid: this.isCacheValid(cached),
    };
  }

  /**
   * Subscribe to real-time configuration changes
   */
  public subscribeToConfigChanges(
    schoolId: string,
    onConfigChange: (config: AdminConfig) => void
  ): () => void {
    console.log('Setting up real-time configuration subscription for school:', schoolId);

    // Subscribe to geofence changes
    const geofenceSubscription = supabase
      .channel(`geofences-${schoolId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'school_geofences',
          filter: `school_id=eq.${schoolId}`,
        },
        async (payload) => {
          console.log('Geofence configuration changed:', payload);
          try {
            const freshConfig = await this.fetchAdminConfig(schoolId);
            onConfigChange(freshConfig);
          } catch (error) {
            console.error('Failed to fetch updated configuration:', error);
          }
        }
      )
      .subscribe();

    // Subscribe to settings changes
    const settingsSubscription = supabase
      .channel(`settings-${schoolId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'teacher_attendance_settings',
          filter: `school_id=eq.${schoolId}`,
        },
        async (payload) => {
          console.log('Attendance settings changed:', payload);
          try {
            const freshConfig = await this.fetchAdminConfig(schoolId);
            onConfigChange(freshConfig);
          } catch (error) {
            console.error('Failed to fetch updated configuration:', error);
          }
        }
      )
      .subscribe();

    // Return cleanup function
    return () => {
      console.log('Cleaning up configuration subscriptions');
      supabase.removeChannel(geofenceSubscription);
      supabase.removeChannel(settingsSubscription);
    };
  }

  /**
   * Test configuration by simulating attendance check
   */
  public async testConfiguration(
    schoolId: string,
    testLocation: { latitude: number; longitude: number }
  ): Promise<{
    success: boolean;
    message: string;
    details: any;
  }> {
    try {
      const config = await this.getAdminConfig(schoolId);
      const validation = this.validateSchoolConfig(config);

      if (!validation.isValid) {
        return {
          success: false,
          message: 'Configuration validation failed',
          details: { issues: validation.issues },
        };
      }

      // Test location against geofences
      const { LocationService } = await import('./locationService');
      const locationService = LocationService.getInstance();

      let withinAnyGeofence = false;
      const geofenceResults = config.geofences.map(geofence => {
        const result = locationService.isWithinGeofence(
          { ...testLocation, accuracy: 10, timestamp: Date.now() },
          geofence
        );
        if (result.withinGeofence) {
          withinAnyGeofence = true;
        }
        return {
          name: geofence.name,
          withinGeofence: result.withinGeofence,
          distance: result.distance,
        };
      });

      return {
        success: withinAnyGeofence,
        message: withinAnyGeofence 
          ? 'Test location is within configured geofences'
          : 'Test location is outside all configured geofences',
        details: {
          testLocation,
          geofenceResults,
          totalGeofences: config.geofences.length,
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Configuration test failed: ${error.message}`,
        details: { error: error.message },
      };
    }
  }
}
