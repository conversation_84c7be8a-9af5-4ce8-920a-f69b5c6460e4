'use client'

import React, { useState, useEffect } from 'react'
import { useAttendanceStore } from '@/stores/attendanceStore'
import { AttendanceStatus, Student } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Calendar, Clock, Users, CheckCircle, XCircle, AlertCircle, Shield } from 'lucide-react'
import { cn } from '@/lib/utils'

interface AttendanceRecord {
  studentId: string
  status: AttendanceStatus
  notes?: string
}

const statusConfig = {
  present: { 
    label: 'Present', 
    color: 'bg-green-500', 
    icon: CheckCircle, 
    badgeVariant: 'default' as const 
  },
  absent: { 
    label: 'Absent', 
    color: 'bg-red-500', 
    icon: XCircle, 
    badgeVariant: 'destructive' as const 
  },
  late: { 
    label: 'Late', 
    color: 'bg-yellow-500', 
    icon: Clock, 
    badgeVariant: 'secondary' as const 
  },
  excused: { 
    label: 'Excused', 
    color: 'bg-blue-500', 
    icon: Shield, 
    badgeVariant: 'outline' as const 
  }
}

export default function AttendanceMarking() {
  const {
    classes,
    currentClass,
    attendance,
    loading,
    error,
    fetchClasses,
    fetchAttendanceForClass,
    setCurrentClass,
    bulkMarkAttendance,
    markAttendance,
    subscribeToAttendance,
    clearError
  } = useAttendanceStore()

  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [attendanceRecords, setAttendanceRecords] = useState<Record<string, AttendanceRecord>>({})
  const [bulkStatus, setBulkStatus] = useState<AttendanceStatus>('present')
  const [globalNotes, setGlobalNotes] = useState('')
  const [hasChanges, setHasChanges] = useState(false)
  const [realtimeSubscription, setRealtimeSubscription] = useState<(() => void) | null>(null)

  useEffect(() => {
    fetchClasses()
  }, [fetchClasses])

  useEffect(() => {
    if (currentClass) {
      fetchAttendanceForClass(currentClass.id, selectedDate)

      // Set up real-time subscription for the current class
      if (realtimeSubscription) {
        realtimeSubscription() // Unsubscribe from previous class
      }

      const unsubscribe = subscribeToAttendance(currentClass.id)
      setRealtimeSubscription(() => unsubscribe)
    }

    // Cleanup subscription when component unmounts or class changes
    return () => {
      if (realtimeSubscription) {
        realtimeSubscription()
      }
    }
  }, [currentClass, selectedDate, fetchAttendanceForClass, subscribeToAttendance])

  useEffect(() => {
    // Initialize attendance records from existing data
    if (currentClass && attendance.length > 0) {
      const records: Record<string, AttendanceRecord> = {}
      attendance.forEach(record => {
        records[record.student_id] = {
          studentId: record.student_id,
          status: record.status,
          notes: record.notes || ''
        }
      })
      setAttendanceRecords(records)
    } else if (currentClass) {
      // Initialize with default 'present' status for all students
      const records: Record<string, AttendanceRecord> = {}
      currentClass.students.forEach(student => {
        records[student.id] = {
          studentId: student.id,
          status: 'present',
          notes: ''
        }
      })
      setAttendanceRecords(records)
    }
    setHasChanges(false)
  }, [currentClass, attendance])

  const handleClassChange = (classId: string) => {
    setCurrentClass(classId)
    setHasChanges(false)
  }

  const handleStatusChange = (studentId: string, status: AttendanceStatus) => {
    setAttendanceRecords(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        status
      }
    }))
    setHasChanges(true)
  }

  const handleNotesChange = (studentId: string, notes: string) => {
    setAttendanceRecords(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        notes
      }
    }))
    setHasChanges(true)
  }

  const handleBulkStatusChange = () => {
    if (!currentClass) return

    const updatedRecords: Record<string, AttendanceRecord> = {}
    currentClass.students.forEach(student => {
      updatedRecords[student.id] = {
        studentId: student.id,
        status: bulkStatus,
        notes: globalNotes
      }
    })
    setAttendanceRecords(updatedRecords)
    setHasChanges(true)
  }

  const handleSaveAttendance = async () => {
    if (!currentClass) return

    try {
      const records = Object.values(attendanceRecords).map(record => ({
        studentId: record.studentId,
        classId: currentClass.id,
        status: record.status,
        notes: record.notes
      }))

      await bulkMarkAttendance(records, selectedDate)
      setHasChanges(false)
    } catch (error) {
      console.error('Error saving attendance:', error)
    }
  }

  const getAttendanceSummary = () => {
    const records = Object.values(attendanceRecords)
    return {
      total: records.length,
      present: records.filter(r => r.status === 'present').length,
      absent: records.filter(r => r.status === 'absent').length,
      late: records.filter(r => r.status === 'late').length,
      excused: records.filter(r => r.status === 'excused').length
    }
  }

  const summary = getAttendanceSummary()

  if (loading && classes.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading classes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-destructive/15 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-destructive">{error}</p>
            <Button variant="ghost" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </div>
        </div>
      )}

      {/* Class and Date Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Attendance Marking
          </CardTitle>
          <CardDescription>
            Mark attendance for any class in your school
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Select Class</label>
              <Select value={currentClass?.id || ''} onValueChange={handleClassChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a class" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.class_name} - Grade {cls.grade} {cls.section && `(${cls.section})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Date</label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>

          {/* Bulk Actions */}
          {currentClass && (
            <div className="flex flex-wrap items-center gap-4 p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Bulk Action:</span>
                <Select value={bulkStatus} onValueChange={(value: AttendanceStatus) => setBulkStatus(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(statusConfig).map(([status, config]) => (
                      <SelectItem key={status} value={status}>
                        {config.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="outline" size="sm" onClick={handleBulkStatusChange}>
                  Apply to All
                </Button>
              </div>
              <div className="flex-1 min-w-48">
                <Textarea
                  placeholder="Global notes (optional)"
                  value={globalNotes}
                  onChange={(e) => setGlobalNotes(e.target.value)}
                  className="h-8 resize-none"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Attendance Summary */}
      {currentClass && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Attendance Summary</CardTitle>
            <CardDescription>
              {currentClass.class_name} - {selectedDate}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{summary.total}</div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
              {Object.entries(statusConfig).map(([status, config]) => {
                const count = summary[status as keyof typeof summary] as number
                return (
                  <div key={status} className="text-center">
                    <div className="text-2xl font-bold">{count}</div>
                    <Badge variant={config.badgeVariant} className="text-xs">
                      {config.label}
                    </Badge>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Student List */}
      {currentClass && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Student Roster</CardTitle>
              {hasChanges && (
                <Button onClick={handleSaveAttendance} disabled={loading}>
                  {loading ? 'Saving...' : 'Save Attendance'}
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {currentClass.students.map(student => {
                const record = attendanceRecords[student.id]
                const config = statusConfig[record?.status || 'present']
                const Icon = config.icon

                return (
                  <div key={student.id} className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{student.name}</div>
                      <div className="text-sm text-muted-foreground">
                        ID: {student.student_id} • Grade: {student.grade}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <Select
                        value={record?.status || 'present'}
                        onValueChange={(value: AttendanceStatus) => handleStatusChange(student.id, value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(statusConfig).map(([status, statusConfig]) => (
                            <SelectItem key={status} value={status}>
                              <div className="flex items-center gap-2">
                                <statusConfig.icon className="h-4 w-4" />
                                {statusConfig.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <div className="w-48">
                        <Textarea
                          placeholder="Notes (optional)"
                          value={record?.notes || ''}
                          onChange={(e) => handleNotesChange(student.id, e.target.value)}
                          className="h-8 resize-none text-xs"
                        />
                      </div>

                      <Badge variant={config.badgeVariant} className="min-w-20 justify-center">
                        <Icon className="h-3 w-3 mr-1" />
                        {config.label}
                      </Badge>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {!currentClass && classes.length > 0 && (
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2" />
              <p>Select a class to start marking attendance</p>
            </div>
          </CardContent>
        </Card>
      )}

      {classes.length === 0 && !loading && (
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center text-muted-foreground">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <p>No classes available</p>
              <p className="text-sm">Create classes first to start marking attendance</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
