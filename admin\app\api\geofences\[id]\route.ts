import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return NextResponse.json({ error: 'School not found' }, { status: 404 })
    }

    // Get specific geofence
    const { data: geofence, error } = await supabase
      .from('school_geofences')
      .select('*')
      .eq('id', params.id)
      .eq('school_id', schoolAdmin.school_id)
      .single()

    if (error || !geofence) {
      return NextResponse.json({ error: 'Geofence not found' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: geofence
    })

  } catch (error) {
    console.error('Error in geofence GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return NextResponse.json({ error: 'School not found' }, { status: 404 })
    }

    const body = await request.json()
    const {
      name,
      center_latitude,
      center_longitude,
      radius_meters,
      allowed_check_in_start,
      allowed_check_in_end,
      allowed_check_out_start,
      allowed_check_out_end,
      allowed_days,
      is_active
    } = body

    // Validation
    if (center_latitude !== undefined && (center_latitude < -90 || center_latitude > 90)) {
      return NextResponse.json({
        error: 'Invalid latitude. Must be between -90 and 90'
      }, { status: 400 })
    }

    if (center_longitude !== undefined && (center_longitude < -180 || center_longitude > 180)) {
      return NextResponse.json({
        error: 'Invalid longitude. Must be between -180 and 180'
      }, { status: 400 })
    }

    if (radius_meters !== undefined && (radius_meters < 10 || radius_meters > 1000)) {
      return NextResponse.json({
        error: 'Invalid radius. Must be between 10 and 1000 meters'
      }, { status: 400 })
    }

    // Update geofence
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (name !== undefined) updateData.name = name
    if (center_latitude !== undefined) updateData.center_latitude = parseFloat(center_latitude)
    if (center_longitude !== undefined) updateData.center_longitude = parseFloat(center_longitude)
    if (radius_meters !== undefined) updateData.radius_meters = parseInt(radius_meters)
    if (allowed_check_in_start !== undefined) updateData.allowed_check_in_start = allowed_check_in_start
    if (allowed_check_in_end !== undefined) updateData.allowed_check_in_end = allowed_check_in_end
    if (allowed_check_out_start !== undefined) updateData.allowed_check_out_start = allowed_check_out_start
    if (allowed_check_out_end !== undefined) updateData.allowed_check_out_end = allowed_check_out_end
    if (allowed_days !== undefined) updateData.allowed_days = allowed_days
    if (is_active !== undefined) updateData.is_active = is_active

    const { data: geofence, error } = await supabase
      .from('school_geofences')
      .update(updateData)
      .eq('id', params.id)
      .eq('school_id', schoolAdmin.school_id)
      .select()
      .single()

    if (error) {
      console.error('Error updating geofence:', error)
      return NextResponse.json({ error: 'Failed to update geofence' }, { status: 500 })
    }

    if (!geofence) {
      return NextResponse.json({ error: 'Geofence not found' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: geofence,
      message: 'Geofence updated successfully'
    })

  } catch (error) {
    console.error('Error in geofence PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return NextResponse.json({ error: 'School not found' }, { status: 404 })
    }

    // Delete geofence
    const { error } = await supabase
      .from('school_geofences')
      .delete()
      .eq('id', params.id)
      .eq('school_id', schoolAdmin.school_id)

    if (error) {
      console.error('Error deleting geofence:', error)
      return NextResponse.json({ error: 'Failed to delete geofence' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Geofence deleted successfully'
    })

  } catch (error) {
    console.error('Error in geofence DELETE:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
