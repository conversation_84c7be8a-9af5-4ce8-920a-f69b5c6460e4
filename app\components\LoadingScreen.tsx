import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';

interface LoadingScreenProps {
  message?: string;
}

export default function LoadingScreen({ message = 'Loading...' }: LoadingScreenProps) {
  return (
    <View className="flex-1 justify-center items-center bg-light-background dark:bg-dark-background">
      <ActivityIndicator size="large" color="#3B82F6" />
      <Text className="text-neutral-600 dark:text-neutral-400 font-rubik mt-4">
        {message}
      </Text>
    </View>
  );
}
