'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useTeacherStore, TeacherCredentials } from '@/stores/teacherStore'
import { generateTeacherId } from '@/lib/password-utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import {
  Plus,
  Edit,
  Trash2,
  User,
  Mail,
  Phone,
  BookOpen,
  Building,
  GraduationCap,
  Users,
  AlertCircle,
  RefreshCw,
  Copy,
  CheckCircle,
  X
} from 'lucide-react'

interface TeacherFormData {
  name: string
  email: string
  phone: string
  subject: string
  department: string
  teacherId: string
}

interface TeacherFormProps {
  formData: TeacherFormData
  setFormData: React.Dispatch<React.SetStateAction<TeacherFormData>>
  onSubmit: (e: React.FormEvent) => void
  onCancel: () => void
  loading: boolean
  editingTeacher: any
  onNameChange?: (name: string) => void
  onGenerateTeacherId?: () => void
}

const TeacherForm = React.memo(({
  formData,
  setFormData,
  onSubmit,
  onCancel,
  loading,
  editingTeacher,
  onNameChange,
  onGenerateTeacherId
}: TeacherFormProps) => {
  return (
    <form onSubmit={onSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="teacherId">Employee ID *</Label>
          <div className="flex gap-2">
            <Input
              id="teacherId"
              value={formData.teacherId}
              onChange={(e) => setFormData(prev => ({ ...prev, teacherId: e.target.value }))}
              placeholder="e.g., T2024JD001"
              required
            />
            {!editingTeacher && onGenerateTeacherId && (
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={onGenerateTeacherId}
                title="Generate new Employee ID"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
          </div>
          <p className="text-xs text-muted-foreground">
            Auto-generated when you enter the name. You can modify it if needed.
          </p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="name">Full Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => {
              const name = e.target.value
              if (onNameChange) {
                onNameChange(name)
              } else {
                setFormData(prev => ({ ...prev, name }))
              }
            }}
            placeholder="Enter teacher's full name"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="email">Email Address *</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            placeholder="Enter email address"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            type="tel"
            value={formData.phone}
            onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            placeholder="Enter phone number"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="subject">Subject Specialization</Label>
          <Input
            id="subject"
            value={formData.subject}
            onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
            placeholder="e.g., Mathematics, English, Science"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="department">Department</Label>
          <Input
            id="department"
            value={formData.department}
            onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
            placeholder="e.g., Science, Arts, Commerce"
          />
        </div>
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? 'Saving...' : editingTeacher ? 'Update Teacher' : 'Add Teacher'}
        </Button>
      </div>
    </form>
  )
})

export default function TeacherManagement() {
  const {
    teachers,
    loading,
    error,
    fetchTeachers,
    addTeacher,
    updateTeacher,
    deleteTeacher,
    clearError
  } = useTeacherStore()

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [editingTeacher, setEditingTeacher] = useState<any>(null)
  const [teacherToDelete, setTeacherToDelete] = useState<any>(null)
  const [newTeacherCredentials, setNewTeacherCredentials] = useState<TeacherCredentials | null>(null)
  const [formData, setFormData] = useState<TeacherFormData>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    department: '',
    teacherId: ''
  })

  useEffect(() => {
    fetchTeachers()
  }, [fetchTeachers])

  // Auto-generate teacher ID when name changes
  const handleNameChange = useCallback((name: string) => {
    setFormData(prev => {
      const newData = { ...prev, name }
      // Auto-generate teacher ID if it's empty or was auto-generated
      if (!newData.teacherId || newData.teacherId.startsWith('T20')) {
        newData.teacherId = generateTeacherId(name)
      }
      return newData
    })
  }, [])

  // Generate new teacher ID
  const handleGenerateTeacherId = useCallback(() => {
    if (formData.name) {
      setFormData(prev => ({
        ...prev,
        teacherId: generateTeacherId(prev.name)
      }))
    }
  }, [formData.name])

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (editingTeacher) {
        await updateTeacher(editingTeacher.id, formData)
        setIsEditDialogOpen(false)
        setEditingTeacher(null)
      } else {
        console.log('Adding teacher:', formData)
        const result = await addTeacher(formData)
        if (!error && result) {
          // Show the generated credentials to admin
          setNewTeacherCredentials({
            teacherId: result.teacherId,
            temporaryPassword: result.temporaryPassword,
            message: result.message
          })
        }
        setIsAddDialogOpen(false)
      }

      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        department: '',
        teacherId: ''
      })
    } catch (error) {
      console.error('Error submitting form:', error)
    }
  }, [editingTeacher, formData, updateTeacher, addTeacher, error])

  const handleEdit = useCallback((teacher: any) => {
    setEditingTeacher(teacher)
    setFormData({
      name: teacher.name,
      email: teacher.email,
      phone: teacher.phone || '',
      subject: teacher.subject || '',
      department: teacher.department || '',
      teacherId: teacher.teacher_id
    })
    setIsEditDialogOpen(true)
  }, [])

  const handleDelete = useCallback((teacher: any) => {
    setTeacherToDelete(teacher)
    setIsDeleteDialogOpen(true)
  }, [])

  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      department: '',
      teacherId: ''
    })
    setEditingTeacher(null)
  }, [])

  const handleCancel = useCallback(() => {
    resetForm()
    setIsAddDialogOpen(false)
    setIsEditDialogOpen(false)
  }, [resetForm])

  const confirmDelete = useCallback(async () => {
    if (!teacherToDelete) return

    try {
      await deleteTeacher(teacherToDelete.id)
      setIsDeleteDialogOpen(false)
      setTeacherToDelete(null)
    } catch (error) {
      // Error will be handled by the store and displayed in the UI
      console.error('Delete failed:', error)
    }
  }, [teacherToDelete, deleteTeacher])

  if (loading && teachers.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading teachers...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-destructive/15 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-destructive">{error}</p>
            <Button variant="ghost" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex-1"></div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add Teacher
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Add New Teacher</DialogTitle>
              <DialogDescription>
                Add a new teacher to your school
              </DialogDescription>
            </DialogHeader>
            <TeacherForm
              formData={formData}
              setFormData={setFormData}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              loading={loading}
              editingTeacher={editingTeacher}
              onNameChange={handleNameChange}
              onGenerateTeacherId={handleGenerateTeacherId}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Teachers Grid */}
      {teachers.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <Users className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No teachers found</h3>
            <p className="text-muted-foreground text-center mb-4">
              Get started by adding your first teacher to the system.
            </p>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Teacher
                </Button>
              </DialogTrigger>
            </Dialog>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teachers.map((teacher) => (
            <Card key={teacher.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{teacher.name}</CardTitle>
                    <CardDescription className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {teacher.teacher_id}
                    </CardDescription>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(teacher)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(teacher)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{teacher.email}</span>
                </div>
                
                {teacher.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{teacher.phone}</span>
                  </div>
                )}

                {teacher.subject && (
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{teacher.subject}</span>
                  </div>
                )}

                {teacher.department && (
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{teacher.department}</span>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <GraduationCap className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{teacher.classCount || 0} classes assigned</span>
                </div>

                {teacher.classes && teacher.classes.length > 0 && (
                  <div className="flex flex-wrap gap-1 pt-2">
                    {teacher.classes.map(classItem => (
                      <Badge key={classItem.id} variant="secondary" className="text-xs">
                        {classItem.class_name}
                      </Badge>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Teacher</DialogTitle>
            <DialogDescription>
              Update teacher information
            </DialogDescription>
          </DialogHeader>
          <TeacherForm
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={loading}
            editingTeacher={editingTeacher}
            onNameChange={handleNameChange}
            onGenerateTeacherId={handleGenerateTeacherId}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Delete Teacher</DialogTitle>
            <DialogDescription>
              Are you sure you want to permanently delete {teacherToDelete?.name}?
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>This will permanently delete:</strong>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• Teacher profile and account</li>
                  <li>• All class assignments</li>
                  <li>• Login credentials</li>
                </ul>
                <p className="mt-2 text-sm">
                  <strong>Note:</strong> Attendance records will be preserved but transferred to the current admin.
                </p>
              </AlertDescription>
            </Alert>

            {teacherToDelete?.classCount > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-yellow-800">Active Class Assignments</p>
                    <p className="text-yellow-700 mt-1">
                      This teacher is currently assigned to {teacherToDelete?.classCount} class(es).
                      These assignments will be automatically removed.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <p className="text-sm text-muted-foreground font-medium">
              This action cannot be undone.
            </p>
            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDelete}
                disabled={loading}
              >
                {loading ? 'Deleting...' : 'Delete Teacher'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Teacher Credentials Dialog */}
      <Dialog open={!!newTeacherCredentials} onOpenChange={() => setNewTeacherCredentials(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Teacher Account Created Successfully!
            </DialogTitle>
            <DialogDescription>
              The teacher account has been created. Please share these credentials with the teacher.
            </DialogDescription>
          </DialogHeader>

          {newTeacherCredentials && (
            <div className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {newTeacherCredentials.message}
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div>
                    <Label className="text-sm font-medium">Employee ID</Label>
                    <p className="text-sm text-muted-foreground">{newTeacherCredentials.teacherId}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigator.clipboard.writeText(newTeacherCredentials.teacherId)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div>
                    <Label className="text-sm font-medium">Temporary Password</Label>
                    <p className="text-sm text-muted-foreground font-mono">{newTeacherCredentials.temporaryPassword}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigator.clipboard.writeText(newTeacherCredentials.temporaryPassword)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  The teacher must change this password on their first login. Keep these credentials secure.
                </AlertDescription>
              </Alert>
            </div>
          )}

          <div className="flex justify-end">
            <Button onClick={() => setNewTeacherCredentials(null)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
