import * as SecureStore from 'expo-secure-store';

// Cache configuration
export const CACHE_CONFIG = {
  SESSION_CHECK_INTERVAL: 10 * 60 * 1000, // 10 minutes
  STUDENT_DATA_TTL: 5 * 60 * 1000, // 5 minutes
  ATTENDANCE_DATA_TTL: 2 * 60 * 1000, // 2 minutes
  TEACHER_DATA_TTL: 5 * 60 * 1000, // 5 minutes
  NETWORK_RETRY_DELAY: 30 * 1000, // 30 seconds
} as const;

// Cache keys
export const CACHE_KEYS = {
  LAST_SESSION_CHECK: 'cache_last_session_check',
  STUDENT_DATA: 'cache_student_data',
  ATTENDANCE_DATA: 'cache_attendance_data',
  TEACHER_DATA: 'cache_teacher_data',
  NETWORK_STATE: 'cache_network_state',
} as const;

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

/**
 * Store data in cache with TTL
 */
export const setCacheItem = async <T>(key: string, data: T, ttl: number): Promise<void> => {
  try {
    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
    };
    await SecureStore.setItemAsync(key, JSON.stringify(cacheItem));
  } catch (error) {
    console.error('Error setting cache item:', error);
  }
};

/**
 * Get data from cache if not expired
 */
export const getCacheItem = async <T>(key: string): Promise<T | null> => {
  try {
    const cached = await SecureStore.getItemAsync(key);
    if (!cached) return null;

    const cacheItem: CacheItem<T> = JSON.parse(cached);
    const age = Date.now() - cacheItem.timestamp;

    if (age > cacheItem.ttl) {
      // Cache expired, remove it
      await SecureStore.deleteItemAsync(key);
      return null;
    }

    return cacheItem.data;
  } catch (error) {
    console.error('Error getting cache item:', error);
    return null;
  }
};

/**
 * Clear specific cache item
 */
export const clearCacheItem = async (key: string): Promise<void> => {
  try {
    await SecureStore.deleteItemAsync(key);
  } catch (error) {
    console.error('Error clearing cache item:', error);
  }
};

/**
 * Clear all cache items
 */
export const clearAllCache = async (): Promise<void> => {
  try {
    const keys = Object.values(CACHE_KEYS);
    await Promise.all(keys.map(key => SecureStore.deleteItemAsync(key)));
  } catch (error) {
    console.error('Error clearing all cache:', error);
  }
};

/**
 * Check if enough time has passed since last session check
 */
export const shouldCheckSession = async (): Promise<boolean> => {
  try {
    const lastCheck = await getCacheItem<number>(CACHE_KEYS.LAST_SESSION_CHECK);
    if (!lastCheck) return true;

    const timeSinceLastCheck = Date.now() - lastCheck;
    return timeSinceLastCheck > CACHE_CONFIG.SESSION_CHECK_INTERVAL;
  } catch (error) {
    console.error('Error checking session timing:', error);
    return true; // Default to checking if there's an error
  }
};

/**
 * Mark that a session check was performed
 */
export const markSessionChecked = async (): Promise<void> => {
  try {
    await setCacheItem(CACHE_KEYS.LAST_SESSION_CHECK, Date.now(), CACHE_CONFIG.SESSION_CHECK_INTERVAL);
  } catch (error) {
    console.error('Error marking session checked:', error);
  }
};

/**
 * Cache student data
 */
export const cacheStudentData = async (studentId: string, data: any): Promise<void> => {
  const key = `${CACHE_KEYS.STUDENT_DATA}_${studentId}`;
  await setCacheItem(key, data, CACHE_CONFIG.STUDENT_DATA_TTL);
};

/**
 * Get cached student data
 */
export const getCachedStudentData = async (studentId: string): Promise<any | null> => {
  const key = `${CACHE_KEYS.STUDENT_DATA}_${studentId}`;
  return await getCacheItem(key);
};

/**
 * Cache attendance data
 */
export const cacheAttendanceData = async (studentId: string, data: any[]): Promise<void> => {
  const key = `${CACHE_KEYS.ATTENDANCE_DATA}_${studentId}`;
  await setCacheItem(key, data, CACHE_CONFIG.ATTENDANCE_DATA_TTL);
};

/**
 * Get cached attendance data
 */
export const getCachedAttendanceData = async (studentId: string): Promise<any[] | null> => {
  const key = `${CACHE_KEYS.ATTENDANCE_DATA}_${studentId}`;
  return await getCacheItem(key);
};

/**
 * Cache teacher data
 */
export const cacheTeacherData = async (teacherId: string, data: any): Promise<void> => {
  const key = `${CACHE_KEYS.TEACHER_DATA}_${teacherId}`;
  await setCacheItem(key, data, CACHE_CONFIG.TEACHER_DATA_TTL);
};

/**
 * Get cached teacher data
 */
export const getCachedTeacherData = async (teacherId: string): Promise<any | null> => {
  const key = `${CACHE_KEYS.TEACHER_DATA}_${teacherId}`;
  return await getCacheItem(key);
};



/**
 * Throttle function to limit call frequency
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};

/**
 * Network-aware cache strategy
 */
export const isNetworkAvailable = async (): Promise<boolean> => {
  try {
    // Simple network check - try to reach a reliable endpoint
    const response = await fetch('https://www.google.com/favicon.ico', {
      method: 'HEAD',
      cache: 'no-cache',
    });
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * Smart fetch with caching and network awareness
 */
export const smartFetch = async <T>(
  key: string,
  fetchFunction: () => Promise<T>,
  ttl: number = CACHE_CONFIG.STUDENT_DATA_TTL,
  forceRefresh: boolean = false
): Promise<T | null> => {
  try {
    // Check cache first if not forcing refresh
    if (!forceRefresh) {
      const cached = await getCacheItem<T>(key);
      if (cached) {
        console.log(`Cache hit for ${key}`);
        return cached;
      }
    }

    // Check network availability
    const networkAvailable = await isNetworkAvailable();
    if (!networkAvailable) {
      console.log('Network unavailable, returning cached data if available');
      return await getCacheItem<T>(key);
    }

    // Fetch fresh data
    console.log(`Fetching fresh data for ${key}`);
    const data = await fetchFunction();
    
    // Cache the result
    await setCacheItem(key, data, ttl);
    
    return data;
  } catch (error) {
    console.error(`Error in smartFetch for ${key}:`, error);
    
    // Return cached data as fallback
    const cached = await getCacheItem<T>(key);
    if (cached) {
      console.log(`Returning cached data as fallback for ${key}`);
      return cached;
    }
    
    return null;
  }
};

/**
 * Batch cache operations for better performance
 */
export const batchCacheOperations = async (operations: (() => Promise<void>)[]): Promise<void> => {
  try {
    await Promise.all(operations.map(op => op()));
  } catch (error) {
    console.error('Error in batch cache operations:', error);
  }
};
