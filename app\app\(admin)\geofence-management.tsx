import React, { useEffect, useState } from 'react';
import { Alert, Modal, ScrollView, Text, TextInput, TouchableOpacity, View, Switch } from 'react-native';
import { useRouter } from 'expo-router';
import * as Location from 'expo-location';
import { useAuthStore } from '@/stores/authStore';
import { supabase, SchoolGeofence } from '@/lib/supabase';

export default function GeofenceManagement() {
  const router = useRouter();
  const { user } = useAuthStore();
  const [geofences, setGeofences] = useState<SchoolGeofence[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingGeofence, setEditingGeofence] = useState<SchoolGeofence | null>(null);
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    center_latitude: '',
    center_longitude: '',
    radius_meters: '100',
    allowed_check_in_start: '07:00',
    allowed_check_in_end: '09:00',
    allowed_check_out_start: '15:00',
    allowed_check_out_end: '18:00',
    is_active: true,
  });

  useEffect(() => {
    if (user?.role !== 'admin') {
      Alert.alert('Access Denied', 'Only administrators can access this page.');
      router.back();
      return;
    }
    fetchGeofences();
  }, [user]);

  const fetchGeofences = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('school_geofences')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setGeofences(data || []);
    } catch (error: any) {
      Alert.alert('Error', `Failed to fetch geofences: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is required to get current location.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      setCurrentLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      setFormData(prev => ({
        ...prev,
        center_latitude: location.coords.latitude.toString(),
        center_longitude: location.coords.longitude.toString(),
      }));

      Alert.alert('Success', 'Current location has been set as the geofence center.');
    } catch (error: any) {
      Alert.alert('Error', `Failed to get current location: ${error.message}`);
    }
  };

  const handleSaveGeofence = async () => {
    if (!formData.name || !formData.center_latitude || !formData.center_longitude) {
      Alert.alert('Validation Error', 'Please fill in all required fields.');
      return;
    }

    setIsLoading(true);
    try {
      const geofenceData = {
        school_id: user?.profile?.school_id,
        name: formData.name,
        center_latitude: parseFloat(formData.center_latitude),
        center_longitude: parseFloat(formData.center_longitude),
        radius_meters: parseInt(formData.radius_meters),
        allowed_check_in_start: formData.allowed_check_in_start + ':00',
        allowed_check_in_end: formData.allowed_check_in_end + ':00',
        allowed_check_out_start: formData.allowed_check_out_start + ':00',
        allowed_check_out_end: formData.allowed_check_out_end + ':00',
        is_active: formData.is_active,
      };

      let result;
      if (editingGeofence) {
        result = await supabase
          .from('school_geofences')
          .update(geofenceData)
          .eq('id', editingGeofence.id);
      } else {
        result = await supabase
          .from('school_geofences')
          .insert(geofenceData);
      }

      if (result.error) throw result.error;

      Alert.alert('Success', `Geofence ${editingGeofence ? 'updated' : 'created'} successfully!`);
      setShowAddModal(false);
      setEditingGeofence(null);
      resetForm();
      fetchGeofences();
    } catch (error: any) {
      Alert.alert('Error', `Failed to save geofence: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditGeofence = (geofence: SchoolGeofence) => {
    setEditingGeofence(geofence);
    setFormData({
      name: geofence.name,
      center_latitude: geofence.center_latitude.toString(),
      center_longitude: geofence.center_longitude.toString(),
      radius_meters: geofence.radius_meters.toString(),
      allowed_check_in_start: geofence.allowed_check_in_start?.slice(0, 5) || '07:00',
      allowed_check_in_end: geofence.allowed_check_in_end?.slice(0, 5) || '09:00',
      allowed_check_out_start: geofence.allowed_check_out_start?.slice(0, 5) || '15:00',
      allowed_check_out_end: geofence.allowed_check_out_end?.slice(0, 5) || '18:00',
      is_active: geofence.is_active ?? true,
    });
    setShowAddModal(true);
  };

  const handleDeleteGeofence = async (geofence: SchoolGeofence) => {
    Alert.alert(
      'Confirm Delete',
      `Are you sure you want to delete the geofence "${geofence.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('school_geofences')
                .delete()
                .eq('id', geofence.id);

              if (error) throw error;
              Alert.alert('Success', 'Geofence deleted successfully!');
              fetchGeofences();
            } catch (error: any) {
              Alert.alert('Error', `Failed to delete geofence: ${error.message}`);
            }
          },
        },
      ]
    );
  };

  const resetForm = () => {
    setFormData({
      name: '',
      center_latitude: '',
      center_longitude: '',
      radius_meters: '100',
      allowed_check_in_start: '07:00',
      allowed_check_in_end: '09:00',
      allowed_check_out_start: '15:00',
      allowed_check_out_end: '18:00',
      is_active: true,
    });
  };

  return (
    <ScrollView className="flex-1 bg-light-background dark:bg-dark-background">
      {/* Header */}
      <View className="bg-primary-500 pt-12 pb-6 px-6">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-white text-2xl font-rubik-bold">
              Geofence Management
            </Text>
            <Text className="text-primary-100 font-rubik">
              Configure school location boundaries
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => router.back()}
            className="bg-white/20 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-rubik-medium">Back</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View className="px-6 py-6">
        {/* Add New Geofence Button */}
        <TouchableOpacity
          onPress={() => {
            resetForm();
            setEditingGeofence(null);
            setShowAddModal(true);
          }}
          className="bg-primary-500 rounded-xl p-4 mb-6"
        >
          <Text className="text-white text-center font-rubik-semibold text-lg">
            Add New Geofence
          </Text>
        </TouchableOpacity>

        {/* Geofences List */}
        <View className="space-y-4">
          {geofences.map((geofence) => (
            <View key={geofence.id} className="bg-white dark:bg-neutral-800 rounded-xl p-4 shadow-sm">
              <View className="flex-row justify-between items-start mb-3">
                <View className="flex-1">
                  <Text className="text-lg font-rubik-bold text-neutral-900 dark:text-neutral-100">
                    {geofence.name}
                  </Text>
                  <Text className="text-sm text-neutral-500 dark:text-neutral-400">
                    Radius: {geofence.radius_meters}m
                  </Text>
                </View>
                <View className={`px-3 py-1 rounded-full ${
                  geofence.is_active ? 'bg-green-100' : 'bg-red-100'
                }`}>
                  <Text className={`text-sm font-rubik-medium ${
                    geofence.is_active ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {geofence.is_active ? 'Active' : 'Inactive'}
                  </Text>
                </View>
              </View>

              <View className="space-y-2 mb-4">
                <Text className="text-sm text-neutral-600 dark:text-neutral-400">
                  📍 Location: {geofence.center_latitude.toFixed(6)}, {geofence.center_longitude.toFixed(6)}
                </Text>
                <Text className="text-sm text-neutral-600 dark:text-neutral-400">
                  🕐 Check-in: {geofence.allowed_check_in_start} - {geofence.allowed_check_in_end}
                </Text>
                <Text className="text-sm text-neutral-600 dark:text-neutral-400">
                  🕕 Check-out: {geofence.allowed_check_out_start} - {geofence.allowed_check_out_end}
                </Text>
              </View>

              <View className="flex-row space-x-3">
                <TouchableOpacity
                  onPress={() => handleEditGeofence(geofence)}
                  className="flex-1 bg-blue-500 rounded-lg py-2"
                >
                  <Text className="text-white text-center font-rubik-medium">Edit</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => handleDeleteGeofence(geofence)}
                  className="flex-1 bg-red-500 rounded-lg py-2"
                >
                  <Text className="text-white text-center font-rubik-medium">Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}

          {geofences.length === 0 && !isLoading && (
            <View className="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 text-center">
              <Text className="text-gray-500 dark:text-gray-400 font-rubik text-center">
                No geofences configured yet. Add your first geofence to enable location-based attendance.
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Add/Edit Geofence Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View className="flex-1 bg-light-background dark:bg-dark-background">
          <View className="bg-primary-500 pt-12 pb-6 px-6">
            <View className="flex-row justify-between items-center">
              <Text className="text-white text-xl font-rubik-bold">
                {editingGeofence ? 'Edit Geofence' : 'Add New Geofence'}
              </Text>
              <TouchableOpacity
                onPress={() => setShowAddModal(false)}
                className="bg-white/20 px-4 py-2 rounded-lg"
              >
                <Text className="text-white font-rubik-medium">Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView className="flex-1 px-6 py-6">
            {/* Form fields will be added in the next part */}
            <View className="space-y-4">
              {/* Name */}
              <View>
                <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Geofence Name *
                </Text>
                <TextInput
                  value={formData.name}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
                  placeholder="e.g., Main Campus, Building A"
                  className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
                />
              </View>

              {/* Current Location Button */}
              <TouchableOpacity
                onPress={getCurrentLocation}
                className="bg-blue-500 rounded-xl p-4"
              >
                <Text className="text-white text-center font-rubik-semibold">
                  📍 Use Current Location
                </Text>
              </TouchableOpacity>

              {/* Coordinates */}
              <View className="flex-row space-x-3">
                <View className="flex-1">
                  <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Latitude *
                  </Text>
                  <TextInput
                    value={formData.center_latitude}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, center_latitude: text }))}
                    placeholder="40.7128"
                    keyboardType="numeric"
                    className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
                  />
                </View>
                <View className="flex-1">
                  <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Longitude *
                  </Text>
                  <TextInput
                    value={formData.center_longitude}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, center_longitude: text }))}
                    placeholder="-74.0060"
                    keyboardType="numeric"
                    className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
                  />
                </View>
              </View>

              {/* Radius */}
              <View>
                <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Radius (meters) *
                </Text>
                <TextInput
                  value={formData.radius_meters}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, radius_meters: text }))}
                  placeholder="100"
                  keyboardType="numeric"
                  className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
                />
              </View>

              {/* Time Windows */}
              <View>
                <Text className="text-lg font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-3">
                  Time Windows
                </Text>
                
                <View className="flex-row space-x-3 mb-3">
                  <View className="flex-1">
                    <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Check-in Start
                    </Text>
                    <TextInput
                      value={formData.allowed_check_in_start}
                      onChangeText={(text) => setFormData(prev => ({ ...prev, allowed_check_in_start: text }))}
                      placeholder="07:00"
                      className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
                    />
                  </View>
                  <View className="flex-1">
                    <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Check-in End
                    </Text>
                    <TextInput
                      value={formData.allowed_check_in_end}
                      onChangeText={(text) => setFormData(prev => ({ ...prev, allowed_check_in_end: text }))}
                      placeholder="09:00"
                      className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
                    />
                  </View>
                </View>

                <View className="flex-row space-x-3">
                  <View className="flex-1">
                    <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Check-out Start
                    </Text>
                    <TextInput
                      value={formData.allowed_check_out_start}
                      onChangeText={(text) => setFormData(prev => ({ ...prev, allowed_check_out_start: text }))}
                      placeholder="15:00"
                      className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
                    />
                  </View>
                  <View className="flex-1">
                    <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Check-out End
                    </Text>
                    <TextInput
                      value={formData.allowed_check_out_end}
                      onChangeText={(text) => setFormData(prev => ({ ...prev, allowed_check_out_end: text }))}
                      placeholder="18:00"
                      className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
                    />
                  </View>
                </View>
              </View>

              {/* Active Status */}
              <View className="flex-row justify-between items-center">
                <Text className="text-lg font-rubik-medium text-neutral-900 dark:text-neutral-100">
                  Active
                </Text>
                <Switch
                  value={formData.is_active}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, is_active: value }))}
                />
              </View>

              {/* Save Button */}
              <TouchableOpacity
                onPress={handleSaveGeofence}
                disabled={isLoading}
                className={`w-full py-4 rounded-xl ${isLoading ? 'bg-gray-400' : 'bg-primary-500'}`}
              >
                <Text className="text-white text-center font-rubik-semibold text-lg">
                  {isLoading ? 'Saving...' : (editingGeofence ? 'Update Geofence' : 'Create Geofence')}
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </ScrollView>
  );
}
