import { useAuthStore } from '@/stores/authStore';
import { useStudentStore } from '@/stores/studentStore';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, Modal, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function StudentProfile() {
  const router = useRouter();
  const { user, signOut, changePassword } = useAuthStore();
  const { currentStudent, enrollments, fetchCurrentStudent, fetchStudentClasses } = useStudentStore();
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  useEffect(() => {
    const loadStudentData = async () => {
      await fetchCurrentStudent();
      if (currentStudent?.id) {
        await fetchStudentClasses(currentStudent.id);
      }
    };

    if (user?.id) {
      loadStudentData();
    }
  }, [user?.id]);

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await signOut();
            router.replace('/(auth)/sign-in');
          }
        },
      ]
    );
  };

  const handleChangePassword = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      Alert.alert('Error', 'Please fill in all password fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'New passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return;
    }

    try {
      await changePassword(currentPassword, newPassword);
      Alert.alert('Success', 'Password changed successfully');
      setShowPasswordModal(false);
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const getEnrollmentStats = () => {
    if (!enrollments?.length) return { active: 0, total: 0 };

    const active = enrollments.filter(e => e.status === 'active').length;
    const total = enrollments.length;

    return { active, total };
  };

  const stats = getEnrollmentStats();

  return (
    <ScrollView className="flex-1 bg-light-background dark:bg-dark-background">
      {/* Header */}
      <View className="bg-primary-500 pt-12 pb-6 px-6">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-white text-2xl font-rubik-bold">
              My Profile
            </Text>
            <Text className="text-primary-100 font-rubik">
              Student Dashboard
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleSignOut}
            className="bg-white/20 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-rubik-medium">Sign Out</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View className="px-6 py-6">
        {/* Personal Information */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Personal Information
          </Text>
          
          <View className="flex flex-col gap-3">
            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Name</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentStudent?.name || 'Loading...'}
              </Text>
            </View>
            
            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Email</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentStudent?.email || 'Loading...'}
              </Text>
            </View>
            
            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Grade</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentStudent?.grade || 'Not assigned'}
              </Text>
            </View>
            
            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Student ID</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentStudent?.student_id || 'Not assigned'}
              </Text>
            </View>
          </View>
        </View>

        {/* Class Enrollment Summary */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Class Enrollment
          </Text>

          <View className="flex-row justify-between mb-4">
            <View className="flex-1 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mr-2">
              <Text className="text-2xl font-rubik-bold text-blue-700 dark:text-blue-300">
                {stats.active}
              </Text>
              <Text className="text-sm font-rubik text-blue-700 dark:text-blue-300">
                Active Classes
              </Text>
            </View>

            <View className="flex-1 bg-green-50 dark:bg-green-900/20 rounded-lg p-4 ml-2">
              <Text className="text-2xl font-rubik-bold text-green-700 dark:text-green-300">
                {stats.total}
              </Text>
              <Text className="text-sm font-rubik text-green-700 dark:text-green-300">
                Total Enrolled
              </Text>
            </View>
          </View>
        </View>

        {/* Enrolled Classes */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            My Classes
          </Text>

          {enrollments && enrollments.length > 0 ? (
            <View className="flex flex-col gap-3">
              {enrollments.slice(0, 5).map((enrollment) => (
                <View key={enrollment.id} className="flex-row justify-between items-center py-3 border-b border-neutral-100 dark:border-neutral-700 last:border-b-0">
                  <View className="flex-1">
                    <Text className="font-rubik-bold text-neutral-900 dark:text-neutral-100">
                      {enrollment.class?.name || enrollment.class?.class_name}
                    </Text>
                    <Text className="text-sm font-rubik text-neutral-600 dark:text-neutral-400">
                      {enrollment.class?.subject}
                    </Text>
                    {enrollment.class?.room_number && (
                      <Text className="text-xs font-rubik text-neutral-500 dark:text-neutral-500">
                        Room: {enrollment.class.room_number}
                      </Text>
                    )}
                  </View>
                  <View className={`px-3 py-1 rounded-full ${
                    enrollment.status === 'active'
                      ? 'bg-green-100 dark:bg-green-900/30'
                      : 'bg-gray-100 dark:bg-gray-700'
                  }`}>
                    <Text className={`text-xs font-rubik-medium ${
                      enrollment.status === 'active'
                        ? 'text-green-800 dark:text-green-200'
                        : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      {enrollment.status.charAt(0).toUpperCase() + enrollment.status.slice(1)}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center py-4">
              No classes enrolled yet
            </Text>
          )}
        </View>

        {/* Account Settings */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Account Settings
          </Text>
          
          <TouchableOpacity
            onPress={() => setShowPasswordModal(true)}
            className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4 mb-3"
          >
            <Text className="font-rubik-medium text-primary-700 dark:text-primary-300">
              Change Password
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Password Change Modal */}
      <Modal
        visible={showPasswordModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-light-background dark:bg-dark-background">
          <View className="flex-row justify-between items-center p-6 border-b border-neutral-200 dark:border-neutral-700">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100">
              Change Password
            </Text>
            <TouchableOpacity onPress={() => setShowPasswordModal(false)}>
              <Text className="text-primary-500 font-rubik-medium">Cancel</Text>
            </TouchableOpacity>
          </View>
          
          <View className="p-6">
            <View className="mb-4">
              <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                Current Password
              </Text>
              <TextInput
                value={currentPassword}
                onChangeText={setCurrentPassword}
                placeholder="Enter current password"
                secureTextEntry
                className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
              />
            </View>
            
            <View className="mb-4">
              <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                New Password
              </Text>
              <TextInput
                value={newPassword}
                onChangeText={setNewPassword}
                placeholder="Enter new password"
                secureTextEntry
                className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
              />
            </View>
            
            <View className="mb-6">
              <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                Confirm New Password
              </Text>
              <TextInput
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder="Confirm new password"
                secureTextEntry
                className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
              />
            </View>
            
            <TouchableOpacity
              onPress={handleChangePassword}
              className="w-full py-4 bg-primary-500 rounded-xl"
            >
              <Text className="text-white text-center font-rubik-semibold text-lg">
                Change Password
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
}
