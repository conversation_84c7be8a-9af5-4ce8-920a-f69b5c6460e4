'use client'

import React, { useState, useEffect } from 'react'
import { useAttendanceStore } from '@/stores/attendanceStore'
import { AttendanceStatus } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { 
  Calendar, 
  Clock, 
  Users, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Shield,
  GraduationCap,
  UserCheck,
  Settings,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AttendanceRecord {
  studentId: string
  classId: string
  status: AttendanceStatus
  notes?: string
}

const statusConfig = {
  present: { 
    label: 'Present', 
    color: 'bg-green-500', 
    icon: CheckCircle, 
    badgeVariant: 'default' as const 
  },
  absent: { 
    label: 'Absent', 
    color: 'bg-red-500', 
    icon: XCircle, 
    badgeVariant: 'destructive' as const 
  },
  late: { 
    label: 'Late', 
    color: 'bg-yellow-500', 
    icon: Clock, 
    badgeVariant: 'secondary' as const 
  },
  excused: { 
    label: 'Excused', 
    color: 'bg-blue-500', 
    icon: Shield, 
    badgeVariant: 'outline' as const 
  }
}

function EnhancedAttendanceMarking() {
  const {
    grades = [],
    selectedGrade,
    studentsInGrade = [],
    markingMode = 'individual',
    attendanceByGrade = {},
    loading = false,
    error,
    fetchGrades,
    fetchStudentsByGrade,
    fetchAttendanceByGrade,
    setSelectedGrade,
    setMarkingMode,
    bulkMarkAttendanceByGrade,
    clearError
  } = useAttendanceStore()

  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [attendanceRecords, setAttendanceRecords] = useState<Record<string, AttendanceRecord>>({})
  const [bulkStatus, setBulkStatus] = useState<AttendanceStatus>('present')
  const [globalNotes, setGlobalNotes] = useState('')
  const [hasChanges, setHasChanges] = useState(false)
  const [bulkLoading, setBulkLoading] = useState(false)

  useEffect(() => {
    try {
      if (fetchGrades && typeof fetchGrades === 'function') {
        fetchGrades()
      }
    } catch (error) {
      console.error('Error fetching grades:', error)
    }
  }, [fetchGrades])

  useEffect(() => {
    if (error && clearError && typeof clearError === 'function') {
      const timer = setTimeout(() => clearError(), 5000)
      return () => clearTimeout(timer)
    }
  }, [error, clearError])

  useEffect(() => {
    // Initialize attendance records when students change
    if (studentsInGrade.length > 0) {
      const records: Record<string, AttendanceRecord> = {}
      studentsInGrade.forEach(student => {
        // Check if there's existing attendance for this student
        const existingAttendance = attendanceByGrade[student.id]
        records[student.id] = {
          studentId: student.id,
          classId: student.class_id || '',
          status: existingAttendance?.status || 'present',
          notes: existingAttendance?.notes || ''
        }
      })
      setAttendanceRecords(records)
      setHasChanges(false)
    }
  }, [studentsInGrade, attendanceByGrade])

  // Fetch attendance when grade or date changes
  useEffect(() => {
    if (selectedGrade && fetchAttendanceByGrade && typeof fetchAttendanceByGrade === 'function') {
      fetchAttendanceByGrade(selectedGrade, selectedDate).catch(console.error)
    }
  }, [selectedGrade, selectedDate, fetchAttendanceByGrade])

  const handleGradeChange = (grade: string) => {
    if (setSelectedGrade && typeof setSelectedGrade === 'function') {
      setSelectedGrade(grade)
    }
    setAttendanceRecords({})
    setHasChanges(false)
  }

  const handleStatusChange = (studentId: string, status: AttendanceStatus) => {
    setAttendanceRecords(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        status
      }
    }))
    setHasChanges(true)
  }

  const handleNotesChange = (studentId: string, notes: string) => {
    setAttendanceRecords(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        notes
      }
    }))
    setHasChanges(true)
  }

  const handleBulkStatusChange = async () => {
    if (studentsInGrade.length === 0) return

    setBulkLoading(true)

    try {
      // Update local state first
      const updatedRecords: Record<string, AttendanceRecord> = {}
      studentsInGrade.forEach(student => {
        updatedRecords[student.id] = {
          studentId: student.id,
          classId: student.class_id || '',
          status: bulkStatus,
          notes: globalNotes
        }
      })
      setAttendanceRecords(updatedRecords)

      // Automatically save to database
      const records = Object.values(updatedRecords).filter(record => record.classId)
      await bulkMarkAttendanceByGrade(records, selectedDate)

      setHasChanges(false)

      // Show success feedback
      alert(`Successfully applied ${bulkStatus} status to all ${studentsInGrade.length} students!`)

    } catch (error) {
      console.error('Error applying bulk status:', error)
      alert('Failed to apply bulk status. Please try again.')
      setHasChanges(true)
    } finally {
      setBulkLoading(false)
    }
  }

  const handleSaveAttendance = async () => {
    if (studentsInGrade.length === 0) return

    try {
      const records = Object.values(attendanceRecords).filter(record => record.classId)
      await bulkMarkAttendanceByGrade(records, selectedDate)
      setHasChanges(false)
    } catch (error) {
      console.error('Error saving attendance:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Grade Selection and Mode Toggle */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GraduationCap className="h-5 w-5" />
            Enhanced Attendance Marking
          </CardTitle>
          <CardDescription>
            Mark attendance by grade with bulk or individual options
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Grade Selection */}
            <div>
              <label className="text-sm font-medium mb-2 block">Select Grade</label>
              <Select value={selectedGrade || ''} onValueChange={handleGradeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a grade" />
                </SelectTrigger>
                <SelectContent>
                  {grades.map(grade => (
                    <SelectItem key={grade} value={grade}>
                      Grade {grade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date Selection */}
            <div>
              <label className="text-sm font-medium mb-2 block">Date</label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>

            {/* Marking Mode Toggle */}
            <div>
              <label className="text-sm font-medium mb-2 block">Marking Mode</label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="marking-mode"
                  checked={markingMode === 'bulk'}
                  onCheckedChange={(checked) => setMarkingMode(checked ? 'bulk' : 'individual')}
                />
                <Label htmlFor="marking-mode" className="text-sm">
                  {markingMode === 'bulk' ? 'Bulk Mode' : 'Individual Mode'}
                </Label>
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedGrade && markingMode === 'bulk' && (
            <div className="flex flex-wrap items-center gap-4 p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Bulk Status:</span>
                <Select
                  value={bulkStatus}
                  onValueChange={(value: AttendanceStatus) => setBulkStatus(value)}
                  disabled={bulkLoading || loading}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(statusConfig).map(([status, config]) => (
                      <SelectItem key={status} value={status}>
                        <div className="flex items-center gap-2">
                          <config.icon className="h-4 w-4" />
                          {config.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkStatusChange}
                  disabled={bulkLoading || loading}
                >
                  {bulkLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Applying...
                    </>
                  ) : (
                    'Apply to All'
                  )}
                </Button>
              </div>
              <div className="flex-1 min-w-48">
                <Textarea
                  placeholder="Global notes (optional)"
                  value={globalNotes}
                  onChange={(e) => setGlobalNotes(e.target.value)}
                  className="h-8 resize-none"
                  disabled={bulkLoading || loading}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Empty State */}
      {!selectedGrade && (
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center text-muted-foreground">
              <GraduationCap className="h-8 w-8 mx-auto mb-2" />
              <p>Select a grade to start marking attendance</p>
              <p className="text-sm">Choose a grade from the dropdown above</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Students in Grade */}
      {selectedGrade && studentsInGrade.length === 0 && !loading && (
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2" />
              <p>No students found in Grade {selectedGrade}</p>
              <p className="text-sm">Make sure students are enrolled in classes for this grade</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Student List */}
      {selectedGrade && studentsInGrade.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Grade {selectedGrade} Students ({studentsInGrade.length})
              </CardTitle>
              {hasChanges && (
                <Button onClick={handleSaveAttendance} disabled={loading}>
                  {loading ? 'Saving...' : 'Save Attendance'}
                </Button>
              )}
            </div>
            <CardDescription>
              {markingMode === 'bulk'
                ? 'Bulk mode: Apply same status to all students (auto-saves) or override individually'
                : 'Individual mode: Set status for each student separately, then save'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {studentsInGrade.map(student => {
                const record = attendanceRecords[student.id]
                const config = statusConfig[record?.status || 'present']
                const Icon = config.icon

                return (
                  <div key={student.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={cn("w-3 h-3 rounded-full", config.color)} />
                      <div>
                        <div className="font-medium">{student.name}</div>
                        <div className="text-sm text-muted-foreground">
                          ID: {student.student_id} | Class: {student.class_name} {student.section && `(${student.section})`}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Select
                        value={record?.status || 'present'}
                        onValueChange={(value: AttendanceStatus) => handleStatusChange(student.id, value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(statusConfig).map(([status, statusConfig]) => (
                            <SelectItem key={status} value={status}>
                              <div className="flex items-center gap-2">
                                <statusConfig.icon className="h-4 w-4" />
                                {statusConfig.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      {markingMode === 'individual' && (
                        <div className="w-48">
                          <Textarea
                            placeholder="Notes (optional)"
                            value={record?.notes || ''}
                            onChange={(e) => handleNotesChange(student.id, e.target.value)}
                            className="h-8 resize-none text-xs"
                          />
                        </div>
                      )}

                      <Badge variant={config.badgeVariant} className="min-w-16 justify-center">
                        <Icon className="h-3 w-3 mr-1" />
                        {config.label}
                      </Badge>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Summary */}
            {studentsInGrade.length > 0 && (
              <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Summary for Grade {selectedGrade}:</span>
                  <div className="flex gap-4">
                    {Object.entries(statusConfig).map(([status, config]) => {
                      const count = Object.values(attendanceRecords).filter(r => r.status === status).length
                      return (
                        <div key={status} className="flex items-center gap-1">
                          <config.icon className="h-4 w-4" />
                          <span>{config.label}: {count}</span>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

EnhancedAttendanceMarking.displayName = 'EnhancedAttendanceMarking'

export default EnhancedAttendanceMarking
