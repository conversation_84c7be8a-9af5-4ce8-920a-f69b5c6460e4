import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/authStore';
import {
    CACHE_CONFIG,
    markSessionChecked,
    shouldCheckSession,
    throttle
} from '@/utils/cacheUtils';
import NetInfo from '@react-native-community/netinfo';
import { useCallback, useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';

interface UseSessionMonitorOptions {
  checkInterval?: number; // in milliseconds
  enableNetworkMonitoring?: boolean;
  enableAppStateMonitoring?: boolean;
}

export const useSessionMonitor = (options: UseSessionMonitorOptions = {}) => {
  const {
    checkInterval = CACHE_CONFIG.SESSION_CHECK_INTERVAL, // Use cached interval
    enableNetworkMonitoring = true,
    enableAppStateMonitoring = true,
  } = options;

  const { user, checkSession, signOut } = useAuthStore();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);

  // Throttled session validation to prevent excessive calls
  const throttledValidateSession = useRef(
    throttle(async () => {
      if (!user) return;

      try {
        // Check if enough time has passed since last check
        const shouldCheck = await shouldCheckSession();
        if (!shouldCheck) {
          console.log('Skipping session check - too recent');
          return;
        }

        const { data: { session }, error } = await supabase.auth.getSession();

        if (error || !session) {
          console.log('Session validation failed, signing out');
          await signOut();
        } else {
          // Mark that we successfully checked the session
          await markSessionChecked();
        }
      } catch (error) {
        console.error('Session validation error:', error);
        // Don't sign out on network errors, just log them
      }
    }, 30000) // Throttle to max once per 30 seconds
  ).current;

  // Periodic session validation with caching
  useEffect(() => {
    if (!user) return;

    // Initial validation (throttled)
    throttledValidateSession();

    // Set up periodic validation with longer intervals
    intervalRef.current = setInterval(throttledValidateSession, checkInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [user, checkInterval]);

  // Simple app state change handler with manual debouncing
  const lastAppStateChange = useRef(0);
  const handleAppStateChange = useCallback(async (nextAppState: AppStateStatus) => {
    const now = Date.now();
    if (now - lastAppStateChange.current < 2000) return; // Manual debounce
    lastAppStateChange.current = now;

    // When app becomes active, check session (but throttled)
    if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
      console.log('App became active, checking session');
      throttledValidateSession();
    }
    appStateRef.current = nextAppState;
  }, [throttledValidateSession]);

  // App state monitoring
  useEffect(() => {
    if (!enableAppStateMonitoring || !user) return;

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [user, enableAppStateMonitoring, handleAppStateChange]);

  // Simple network change handler with manual debouncing
  const lastNetworkChange = useRef(0);
  const handleNetworkChange = useCallback((state: any) => {
    const now = Date.now();
    if (now - lastNetworkChange.current < 5000) return; // Manual debounce
    lastNetworkChange.current = now;

    // When network becomes available, check session (but throttled)
    if (state.isConnected && state.isInternetReachable) {
      console.log('Network connectivity restored, checking session');
      // Use throttled validation instead of direct checkSession
      setTimeout(() => {
        throttledValidateSession();
      }, 2000); // Small delay to ensure connection is stable
    }
  }, [throttledValidateSession]);

  // Network connectivity monitoring
  useEffect(() => {
    if (!enableNetworkMonitoring || !user) return;

    const unsubscribe = NetInfo.addEventListener(handleNetworkChange);

    return () => {
      unsubscribe();
    };
  }, [user, enableNetworkMonitoring, handleNetworkChange]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);
};
