import { Student } from '@/lib/supabase';
import { useAuthStore } from '@/stores/authStore';
import { useStudentStore } from '@/stores/studentStore';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, Modal, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function TeacherStudents() {
  const router = useRouter();
  const { user } = useAuthStore();
  const {
    students,
    enrollments,
    isLoading,
    error,
    fetchClassStudents,
    updateStudent,
    clearError
  } = useStudentStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [showStudentModal, setShowStudentModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [showGradePicker, setShowGradePicker] = useState(false);
  const [availableGrades, setAvailableGrades] = useState<string[]>([]);
  const [isUpdatingGrade, setIsUpdatingGrade] = useState(false);
  const [isLoadingGrades, setIsLoadingGrades] = useState(false);

  // Mock classes for now - in real implementation, fetch from teacher's classes
  const teacherClasses = [
    { id: '550e8400-e29b-41d4-a716-446655440001', name: 'Mathematics 101' },
    { id: '550e8400-e29b-41d4-a716-446655440002', name: 'English Literature' },
    { id: '550e8400-e29b-41d4-a716-446655440003', name: 'Science Basics' },
  ];

  useEffect(() => {
    if (user?.role !== 'teacher') {
      Alert.alert('Access Denied', 'This page is only accessible to teachers.');
      router.back();
      return;
    }

    // Load first class by default
    if (teacherClasses.length > 0 && !selectedClass) {
      setSelectedClass(teacherClasses[0].id);
    }
  }, [user]);

  useEffect(() => {
    if (selectedClass) {
      loadClassStudents();
    }
  }, [selectedClass]);

  useEffect(() => {
    fetchAvailableGrades();
  }, [user]);

  const loadClassStudents = async () => {
    if (!selectedClass) {
      console.log('No class selected, skipping student fetch');
      return;
    }

    try {
      console.log('Loading students for class:', selectedClass);
      await fetchClassStudents(selectedClass);
    } catch (error) {
      console.error('Error loading class students:', error);
    }
  };

  const fetchAvailableGrades = async () => {
    setIsLoadingGrades(true);
    try {
      // Import supabase dynamically to avoid import issues
      const { supabase } = await import('@/lib/supabase');

      const { data, error } = await supabase
        .from('classes')
        .select('grade_level')
        .eq('school_id', user?.profile?.school_id)
        .not('grade_level', 'is', null);

      if (error) {
        console.error('Error fetching grades:', error);
        // Fallback to common grade levels
        setAvailableGrades(['9th Grade', '10th Grade', '11th Grade', '12th Grade']);
        return;
      }

      // Extract unique grade levels
      const uniqueGrades = [...new Set(data.map(item => item.grade_level).filter(Boolean))];

      // Add common grades if none found
      if (uniqueGrades.length === 0) {
        setAvailableGrades(['9th Grade', '10th Grade', '11th Grade', '12th Grade']);
      } else {
        setAvailableGrades(uniqueGrades);
      }
    } catch (error) {
      console.error('Error fetching available grades:', error);
      // Fallback to common grade levels
      setAvailableGrades(['9th Grade', '10th Grade', '11th Grade', '12th Grade']);
    } finally {
      setIsLoadingGrades(false);
    }
  };

  const filteredStudents = (students || []).filter(student =>
    student.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (student.student_id && student.student_id.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleStudentPress = (student: Student) => {
    setSelectedStudent(student);
    setShowStudentModal(true);
  };

  const handleUpdateGrade = async (studentId: string, newGrade: string) => {
    setIsUpdatingGrade(true);
    try {
      await updateStudent(studentId, { grade: newGrade });
      Alert.alert('Success', 'Student grade updated successfully!');
      setShowGradePicker(false);
      setShowStudentModal(false);
      setSelectedStudent(null);
    } catch (error: any) {
      Alert.alert('Error', `Failed to update grade: ${error.message}`);
    } finally {
      setIsUpdatingGrade(false);
    }
  };

  const openGradePicker = () => {
    setShowGradePicker(true);
  };

  if (error) {
    return (
      <View className="flex-1 bg-light-background dark:bg-dark-background">
        <View className="bg-secondary-500 pt-12 pb-6 px-6">
          <Text className="text-white text-2xl font-rubik-bold">My Students</Text>
        </View>

        <View className="flex-1 justify-center items-center px-6">
          <View className="bg-red-50 dark:bg-red-900/20 rounded-xl p-6 w-full">
            <Text className="text-red-800 dark:text-red-200 font-rubik-bold text-center mb-2">
              Error Loading Students
            </Text>
            <Text className="text-red-700 dark:text-red-300 font-rubik text-center mb-4">
              {error}
            </Text>
            <TouchableOpacity
              onPress={() => {
                clearError();
                loadClassStudents();
              }}
              className="bg-red-600 rounded-lg py-3"
            >
              <Text className="text-white font-rubik-semibold text-center">Try Again</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-light-background dark:bg-dark-background">
      {/* Header */}
      <View className="bg-secondary-500 pt-12 pb-6 px-6">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-white text-2xl font-rubik-bold">My Students</Text>
            <Text className="text-secondary-100 font-rubik">
              Manage students in your classes
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => router.back()}
            className="bg-white/20 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-rubik-medium">Back</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View className="flex-1 px-6 py-6">
        {/* Class Selection */}
        <View className="mb-6">
          <Text className="text-lg font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-3">
            Select Class
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} className="mb-4">
            {teacherClasses.map((classItem) => (
              <TouchableOpacity
                key={classItem.id}
                onPress={() => setSelectedClass(classItem.id)}
                className={`mr-3 px-4 py-2 rounded-lg ${
                  selectedClass === classItem.id
                    ? 'bg-secondary-500'
                    : 'bg-gray-200 dark:bg-gray-700'
                }`}
              >
                <Text
                  className={`font-rubik-medium ${
                    selectedClass === classItem.id
                      ? 'text-white'
                      : 'text-neutral-700 dark:text-neutral-300'
                  }`}
                >
                  {classItem.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Search Bar */}
        <View className="mb-6">
          <TextInput
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search students by name, email, or ID..."
            className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
            placeholderTextColor="#9CA3AF"
          />
        </View>

        {/* Students List */}
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {isLoading ? (
            <View className="py-8">
              <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center">
                Loading students...
              </Text>
            </View>
          ) : filteredStudents && filteredStudents.length > 0 ? (
            <View className="space-y-4">
              {filteredStudents.map((student) => (
                <TouchableOpacity
                  key={student.id}
                  onPress={() => handleStudentPress(student)}
                  className="bg-white dark:bg-neutral-800 rounded-xl p-5 shadow-sm mb-3"
                >
                  <View className="flex-row justify-between items-start">
                    <View className="flex-1">
                      <Text className="text-lg font-rubik-bold text-neutral-900 dark:text-neutral-100">
                        {student.name}
                      </Text>
                      <Text className="text-sm text-neutral-600 dark:text-neutral-400 font-rubik">
                        {student.email}
                      </Text>
                      {student.student_id && (
                        <Text className="text-sm text-neutral-500 dark:text-neutral-500 font-rubik">
                          ID: {student.student_id}
                        </Text>
                      )}
                      {student.grade && (
                        <Text className="text-sm text-neutral-500 dark:text-neutral-500 font-rubik">
                          Grade: {student.grade}
                        </Text>
                      )}
                    </View>
                    <View className="bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full">
                      <Text className="text-blue-800 dark:text-blue-200 text-xs font-rubik-medium">
                        Active
                      </Text>
                    </View>
                  </View>

                  {student.parent_contact && (
                    <View className="mt-3 pt-3 border-t border-neutral-200 dark:border-neutral-700">
                      <Text className="text-xs text-neutral-500 dark:text-neutral-500 font-rubik">
                        Parent: {student.parent_contact}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View className="py-8">
              <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center">
                {searchQuery ? 'No students found matching your search' : 'No students in this class'}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>

      {/* Student Detail Modal */}
      <Modal
        visible={showStudentModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowStudentModal(false)}
      >
        <View className="flex-1 bg-light-background dark:bg-dark-background">
          <View className="bg-secondary-500 pt-10 pb-4 px-6">
            <View className="flex-row justify-between items-center">
              <Text className="text-white text-xl font-rubik-bold">
                {selectedStudent?.name}
              </Text>
              <TouchableOpacity
                onPress={() => setShowStudentModal(false)}
                className="bg-white/20 px-4 py-2 rounded-lg"
              >
                <Text className="text-white font-rubik-medium">Close</Text>
              </TouchableOpacity>
            </View>
          </View>

          {selectedStudent && (
            <ScrollView className="flex-1 px-6 py-6">
              {/* Student Information */}
              <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
                <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
                  Student Information
                </Text>

                <View className="space-y-3">
                  <View>
                    <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                      Full Name
                    </Text>
                    <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                      {selectedStudent.name}
                    </Text>
                  </View>

                  <View>
                    <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                      Student ID
                    </Text>
                    <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                      {selectedStudent.student_id || 'Not assigned'}
                    </Text>
                  </View>

                  <View>
                    <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                      Email
                    </Text>
                    <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                      {selectedStudent.email}
                    </Text>
                  </View>

                  <View>
                    <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                      Current Grade
                    </Text>
                    <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                      {selectedStudent.grade || 'Not assigned'}
                    </Text>
                  </View>
                </View>
              </View>

              {/* Quick Actions */}
              <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 shadow-sm">
                <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
                  Quick Actions
                </Text>

                <View className="space-y-6">
                  <TouchableOpacity
                    onPress={openGradePicker}
                    className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-5 flex-row items-center"
                  >
                    <Text className="text-2xl mr-3">📝</Text>
                    <View>
                      <Text className="font-rubik-semibold text-blue-800 dark:text-blue-200">
                        Update Grade
                      </Text>
                      <Text className="text-sm font-rubik text-blue-600 dark:text-blue-300">
                        Modify student's current grade
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => {
                      Alert.alert('Coming Soon', 'Attendance marking feature will be available soon.');
                    }}
                    className="bg-green-50 dark:bg-green-900/20 rounded-lg p-5 flex-row items-center"
                  >
                    <Text className="text-2xl mr-3">✅</Text>
                    <View>
                      <Text className="font-rubik-semibold text-green-800 dark:text-green-200">
                        Mark Attendance
                      </Text>
                      <Text className="text-sm font-rubik text-green-600 dark:text-green-300">
                        Record student attendance
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </ScrollView>
          )}
        </View>
      </Modal>

      {/* Grade Picker Modal */}
      <Modal
        visible={showGradePicker}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowGradePicker(false)}
      >
        <View className="flex-1 bg-light-background dark:bg-dark-background">
          <View className="bg-secondary-500 pt-12 pb-6 px-6">
            <View className="flex-row justify-between items-center">
              <Text className="text-white text-xl font-rubik-bold">
                Select Grade
              </Text>
              <TouchableOpacity
                onPress={() => setShowGradePicker(false)}
                className="bg-white/20 px-4 py-2 rounded-lg"
              >
                <Text className="text-white font-rubik-medium">Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView className="flex-1 px-6 py-6">
            <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
              <Text className="text-lg font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
                Available Grade Levels
              </Text>
              <Text className="text-sm font-rubik text-neutral-600 dark:text-neutral-400 mb-6">
                Select a grade level for {selectedStudent?.name}
              </Text>

              <View className="space-y-4">
                {isLoadingGrades ? (
                  <View className="py-8 items-center">
                    <Text className="text-neutral-500 dark:text-neutral-400 font-rubik">
                      Loading grade levels...
                    </Text>
                  </View>
                ) : (
                  availableGrades.map((grade, index) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => {
                        if (selectedStudent?.id && !isUpdatingGrade) {
                          handleUpdateGrade(selectedStudent.id, grade);
                        }
                      }}
                      disabled={isUpdatingGrade}
                      className={`p-5 rounded-lg border-2 ${
                        selectedStudent?.grade === grade
                          ? 'border-secondary-500 bg-secondary-50 dark:bg-secondary-900/20'
                          : 'border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800'
                      } ${isUpdatingGrade ? 'opacity-50' : ''}`}
                    >
                    <View className="flex-row justify-between items-center">
                      <Text className={`font-rubik-semibold ${
                        selectedStudent?.grade === grade
                          ? 'text-secondary-700 dark:text-secondary-300'
                          : 'text-neutral-900 dark:text-neutral-100'
                      }`}>
                        {grade}
                      </Text>
                      {selectedStudent?.grade === grade && (
                        <View className="bg-secondary-500 rounded-full w-6 h-6 items-center justify-center">
                          <Text className="text-white text-xs font-rubik-bold">✓</Text>
                        </View>
                      )}
                    </View>
                  </TouchableOpacity>
                  ))
                )}
              </View>

              {!isLoadingGrades && availableGrades.length === 0 && (
                <View className="py-8">
                  <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center">
                    No grade levels found. Contact your administrator to set up grade levels.
                  </Text>
                </View>
              )}

              {isUpdatingGrade && (
                <View className="mt-6 py-4 items-center">
                  <Text className="text-secondary-600 dark:text-secondary-400 font-rubik">
                    Updating grade...
                  </Text>
                </View>
              )}
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}
