import { useAuthStore } from '@/stores/authStore';
import { useTeacherAttendanceStore } from '@/stores/teacherAttendanceStore';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Modal, Text, TouchableOpacity, View } from 'react-native';

interface TeacherAttendanceModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function TeacherAttendanceModal({ visible, onClose }: TeacherAttendanceModalProps) {
  const { user } = useAuthStore();
  const {
    todayAttendance,
    isLoading,
    error,
    fetchTodayAttendance,
    checkIn,
    checkOut,
    clearError,
  } = useTeacherAttendanceStore();

  const [isProcessing, setIsProcessing] = useState(false);

  const currentTeacher = user?.role === 'teacher' ? user.profile : null;

  useEffect(() => {
    if (visible && currentTeacher?.id) {
      fetchTodayAttendance(currentTeacher.id);
    }
  }, [visible, currentTeacher?.id]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      clearError();
    }
  }, [error]);

  const handleCheckIn = async () => {
    if (!currentTeacher?.id || !currentTeacher?.school_id) {
      Alert.alert('Error', 'Teacher information not available');
      return;
    }

    setIsProcessing(true);
    try {
      await checkIn(currentTeacher.id, currentTeacher.school_id);
      Alert.alert(
        'Success',
        'Check-in successful! Your attendance has been recorded.',
        [{ text: 'OK', onPress: () => {} }]
      );
    } catch (error: any) {
      Alert.alert('Check-in Failed', error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCheckOut = async () => {
    if (!currentTeacher?.id) {
      Alert.alert('Error', 'Teacher information not available');
      return;
    }

    setIsProcessing(true);
    try {
      await checkOut(currentTeacher.id);
      Alert.alert(
        'Success',
        'Check-out successful! Your work hours have been recorded.',
        [{ text: 'OK', onPress: () => {} }]
      );
    } catch (error: any) {
      Alert.alert('Check-out Failed', error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800';
      case 'late':
        return 'bg-yellow-100 text-yellow-800';
      case 'absent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-light-background dark:bg-dark-background">
        {/* Header */}
        <View className="bg-secondary-500 pt-12 pb-6 px-6">
          <View className="flex-row justify-between items-center">
            <Text className="text-white text-xl font-rubik-bold">Mark Attendance</Text>
            <TouchableOpacity
              onPress={onClose}
              className="bg-white/20 px-4 py-2 rounded-lg"
            >
              <Text className="text-white font-rubik-medium">Close</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View className="flex-1 px-6 py-6">
          {/* Admin Configuration Notice */}
          <View className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 mb-4">
            <View className="flex-row items-center mb-2">
              <Text className="text-blue-800 dark:text-blue-200 font-rubik-bold">
                📍 Location-Based Attendance
              </Text>
            </View>
            <Text className="text-blue-700 dark:text-blue-300 font-rubik text-sm">
              • Your school administrator has configured secure location boundaries{'\n'}
              • You must be within school premises to mark attendance{'\n'}
              • GPS location will be verified for security
            </Text>
          </View>

          {isLoading ? (
            <View className="flex-1 justify-center items-center">
              <ActivityIndicator size="large" color="#6366f1" />
              <Text className="text-neutral-600 dark:text-neutral-400 font-rubik mt-4">
                Loading attendance data...
              </Text>
            </View>
          ) : (
            <>
              {/* Today's Status */}
              <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
                <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
                  Today's Attendance
                </Text>

                {todayAttendance ? (
                  <View className="space-y-3">
                    <View className="flex-row justify-between items-center">
                      <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                        Status
                      </Text>
                      <View className={`px-3 py-1 rounded-full ${getStatusColor(todayAttendance.status)}`}>
                        <Text className="text-sm font-rubik-medium capitalize">
                          {todayAttendance.status}
                        </Text>
                      </View>
                    </View>

                    {todayAttendance.check_in_time && (
                      <View className="flex-row justify-between items-center">
                        <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                          Check-in Time
                        </Text>
                        <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                          {formatTime(todayAttendance.check_in_time)}
                        </Text>
                      </View>
                    )}

                    {todayAttendance.check_out_time && (
                      <View className="flex-row justify-between items-center">
                        <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                          Check-out Time
                        </Text>
                        <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                          {formatTime(todayAttendance.check_out_time)}
                        </Text>
                      </View>
                    )}

                    {todayAttendance.work_hours && (
                      <View className="flex-row justify-between items-center">
                        <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                          Work Hours
                        </Text>
                        <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                          {todayAttendance.work_hours} hours
                        </Text>
                      </View>
                    )}

                    {todayAttendance.location_verified && (
                      <View className="flex-row justify-between items-center">
                        <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                          Location Verified
                        </Text>
                        <View className="bg-green-100 px-3 py-1 rounded-full">
                          <Text className="text-green-800 text-sm font-rubik-medium">
                            ✓ Verified
                          </Text>
                        </View>
                      </View>
                    )}
                  </View>
                ) : (
                  <View className="text-center py-8">
                    <Text className="text-neutral-500 dark:text-neutral-400 font-rubik">
                      No attendance record for today
                    </Text>
                  </View>
                )}
              </View>

              {/* Action Buttons */}
              <View className="space-y-4">
                {!todayAttendance?.check_in_time ? (
                  <TouchableOpacity
                    onPress={handleCheckIn}
                    disabled={isProcessing}
                    className={`w-full py-4 rounded-xl ${
                      isProcessing
                        ? 'bg-gray-400'
                        : 'bg-green-500'
                    }`}
                  >
                    {isProcessing ? (
                      <View className="flex-row justify-center items-center">
                        <ActivityIndicator size="small" color="white" />
                        <Text className="text-white text-center font-rubik-semibold text-lg ml-2">
                          Checking In...
                        </Text>
                      </View>
                    ) : (
                      <Text className="text-white text-center font-rubik-semibold text-lg">
                        Check In
                      </Text>
                    )}
                  </TouchableOpacity>
                ) : !todayAttendance?.check_out_time ? (
                  <TouchableOpacity
                    onPress={handleCheckOut}
                    disabled={isProcessing}
                    className={`w-full py-4 rounded-xl ${
                      isProcessing
                        ? 'bg-gray-400'
                        : 'bg-red-500'
                    }`}
                  >
                    {isProcessing ? (
                      <View className="flex-row justify-center items-center">
                        <ActivityIndicator size="small" color="white" />
                        <Text className="text-white text-center font-rubik-semibold text-lg ml-2">
                          Checking Out...
                        </Text>
                      </View>
                    ) : (
                      <Text className="text-white text-center font-rubik-semibold text-lg">
                        Check Out
                      </Text>
                    )}
                  </TouchableOpacity>
                ) : (
                  <View className="bg-gray-100 dark:bg-gray-800 py-4 rounded-xl">
                    <Text className="text-gray-600 dark:text-gray-400 text-center font-rubik-semibold text-lg">
                      Attendance Complete for Today
                    </Text>
                  </View>
                )}
              </View>

              {/* Security Information */}
              <View className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 mt-6">
                <Text className="text-blue-800 dark:text-blue-200 font-rubik-bold mb-2">
                  Security Notice
                </Text>
                <Text className="text-blue-700 dark:text-blue-300 font-rubik text-sm">
                  • Location verification is required for attendance
                  {'\n'}• You must be within school premises
                  {'\n'}• Check-in/out times are restricted to school hours
                  {'\n'}• All attendance actions are logged for security
                </Text>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
}
