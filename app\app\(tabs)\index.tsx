import { useAuthStore } from '@/stores/authStore';
import { useStudentStore } from '@/stores/studentStore';
import { useRouter } from 'expo-router';
import React, { useEffect } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';

export default function HomeScreen() {
  const { user } = useAuthStore();
  const { currentStudent, enrollments, fetchCurrentStudent, fetchStudentClasses } = useStudentStore();
  const router = useRouter();

  useEffect(() => {
    const loadStudentData = async () => {
      if (user?.role === 'student') {
        try {
          await fetchCurrentStudent();
          if (currentStudent?.id) {
            await fetchStudentClasses(currentStudent.id);
          }
        } catch (error) {
          console.error('Error loading student data in home screen:', error);
        }
      }
    };

    if (user) {
      loadStudentData();
    }
  }, [user?.id, user?.role]);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getEnrollmentStats = () => {
    if (!enrollments || !enrollments.length) return { active: 0, total: 0 };

    const active = enrollments.filter(e => e.status === 'active').length;
    const total = enrollments.length;

    return { active, total };
  };

  const stats = getEnrollmentStats();

  if (user?.role === 'student') {
    return (
      <ScrollView className="flex-1 bg-light-background dark:bg-dark-background">
        {/* Header */}
        <View className="bg-primary-500 pt-12 pb-6 px-6">
          <Text className="text-white text-2xl font-rubik-bold">
            {getGreeting()}, {currentStudent?.name?.split(' ')[0] || 'Student'}!
          </Text>
          <Text className="text-primary-100 font-rubik">
            Welcome to your student dashboard
          </Text>
        </View>

        <View className="px-6 py-6">
          {/* Quick Stats */}
          <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Quick Overview
            </Text>

            <View className="flex-row justify-between">
              <View className="flex-1 bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4 mr-2">
                <Text className="text-2xl font-rubik-bold text-primary-700 dark:text-primary-300">
                  {currentStudent?.grade || 'N/A'}
                </Text>
                <Text className="text-sm font-rubik text-primary-700 dark:text-primary-300">
                  Current Grade
                </Text>
              </View>

              <View className="flex-1 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 ml-2">
                <Text className="text-2xl font-rubik-bold text-blue-700 dark:text-blue-300">
                  {stats.active}
                </Text>
                <Text className="text-sm font-rubik text-blue-700 dark:text-blue-300">
                  Active Classes
                </Text>
              </View>
            </View>
          </View>

          {/* Recent Activity */}
          <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Recent Activity
            </Text>

            {enrollments && enrollments.length > 0 ? (
              <View className="flex flex-col gap-3">
                {enrollments.slice(0, 3).map((enrollment) => (
                  <View key={enrollment.id} className="flex-row justify-between items-center py-2">
                    <View>
                      <Text className="font-rubik-medium text-neutral-900 dark:text-neutral-100">
                        {enrollment.class?.name || enrollment.class?.class_name}
                      </Text>
                      <Text className="text-sm font-rubik text-neutral-500 dark:text-neutral-400">
                        {enrollment.class?.subject}
                      </Text>
                    </View>
                    <View className="px-3 py-1 rounded-full bg-green-100 dark:bg-green-900/30">
                      <Text className="text-sm font-rubik-medium text-green-800 dark:text-green-200">
                        {enrollment.status.charAt(0).toUpperCase() + enrollment.status.slice(1)}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center py-4">
                No classes enrolled yet
              </Text>
            )}
          </View>

          {/* Quick Actions */}
          <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 shadow-sm">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Quick Actions
            </Text>

            <TouchableOpacity
              onPress={() => router.push('/student-profile')}
              className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4 mb-3"
            >
              <Text className="font-rubik-medium text-primary-700 dark:text-primary-300">
                View Full Profile
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => router.push('/student-profile')}
              className="bg-secondary-50 dark:bg-secondary-900/20 rounded-lg p-4"
            >
              <Text className="font-rubik-medium text-secondary-700 dark:text-secondary-300">
                View My Profile
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    );
  }

  if (user?.role === 'teacher') {
    return (
      <ScrollView className="flex-1 bg-light-background dark:bg-dark-background">
        {/* Header */}
        <View className="bg-secondary-500 pt-12 pb-6 px-6">
          <Text className="text-white text-2xl font-rubik-bold">
            {getGreeting()}, {user.profile?.name?.split(' ')[0] || 'Teacher'}!
          </Text>
          <Text className="text-secondary-100 font-rubik">
            Ready to manage your students today
          </Text>
        </View>

        <View className="px-6 py-6">
          {/* Welcome Message */}
          <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-2">
              Welcome to School Management
            </Text>
            <Text className="text-neutral-600 dark:text-neutral-400 font-rubik">
              Use the dashboard to manage student grades and attendance efficiently.
            </Text>
          </View>

          {/* Quick Actions */}
          <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 shadow-sm">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Quick Actions
            </Text>

            <TouchableOpacity
              onPress={() => router.push('/teacher-dashboard')}
              className="bg-secondary-50 dark:bg-secondary-900/20 rounded-lg p-4 mb-3"
            >
              <Text className="font-rubik-medium text-secondary-700 dark:text-secondary-300">
                Go to Dashboard
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => router.push('/teacher-profile')}
              className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4 mb-3"
            >
              <Text className="font-rubik-medium text-primary-700 dark:text-primary-300">
                View My Profile
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => router.push('/teacher-dashboard')}
              className="bg-neutral-50 dark:bg-neutral-700/20 rounded-lg p-4"
            >
              <Text className="font-rubik-medium text-neutral-700 dark:text-neutral-300">
                View All Students
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    );
  }

  return (
    <View className="flex-1 justify-center items-center bg-light-background dark:bg-dark-background">
      <Text className="text-neutral-500 dark:text-neutral-400 font-rubik">
        Loading...
      </Text>
    </View>
  );
}
