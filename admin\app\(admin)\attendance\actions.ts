'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/utils/supabase/server'
import { AttendanceStatus } from '@/lib/supabase'

export interface AttendanceRecord {
  studentId: string
  classId: string
  status: AttendanceStatus
  notes?: string
}

export interface AttendanceResult {
  success?: boolean
  error?: string
  message?: string
}

export async function markAttendance(
  records: AttendanceRecord[],
  date?: string
): Promise<AttendanceResult> {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return { error: 'User not authenticated' }
    }

    // Verify user is admin or teacher
    const { data: adminData } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    const { data: teacherData } = await supabase
      .from('teachers')
      .select('id, school_id')
      .eq('user_id', user.id)
      .single()

    if (!adminData && !teacherData) {
      return { error: 'Unauthorized: User is not an admin or teacher' }
    }

    const schoolId = adminData?.school_id || teacherData?.school_id
    const attendanceDate = date || new Date().toISOString().split('T')[0]

    // If user is teacher, verify they can mark attendance for these classes
    if (teacherData && !adminData) {
      const classIds = [...new Set(records.map(r => r.classId))]
      
      const { data: teacherClasses, error: classError } = await supabase
        .from('teacher_classes')
        .select('class_id')
        .eq('teacher_id', teacherData.id)
        .in('class_id', classIds)

      if (classError) {
        return { error: 'Failed to verify class permissions' }
      }

      const authorizedClassIds = teacherClasses?.map(tc => tc.class_id) || []
      const unauthorizedClasses = classIds.filter(id => !authorizedClassIds.includes(id))

      if (unauthorizedClasses.length > 0) {
        return { error: 'Unauthorized: You cannot mark attendance for some of these classes' }
      }
    }

    // Prepare attendance records
    const attendanceRecords = records.map(record => ({
      student_id: record.studentId,
      class_id: record.classId,
      school_id: schoolId,
      attendance_date: attendanceDate,
      status: record.status,
      notes: record.notes,
      marked_by: user.id
    }))

    // Insert/update attendance records
    const { error: insertError } = await supabase
      .from('attendance')
      .upsert(attendanceRecords, {
        onConflict: 'student_id,class_id,attendance_date'
      })

    if (insertError) {
      console.error('Error inserting attendance:', insertError)
      return { error: 'Failed to save attendance records' }
    }

    // Revalidate the attendance page
    revalidatePath('/attendance')

    return { 
      success: true, 
      message: `Successfully marked attendance for ${records.length} students` 
    }

  } catch (error) {
    console.error('Error in markAttendance:', error)
    return { error: 'An unexpected error occurred' }
  }
}

export async function updateAttendanceRecord(
  attendanceId: string,
  status: AttendanceStatus,
  notes?: string
): Promise<AttendanceResult> {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return { error: 'User not authenticated' }
    }

    // Get the attendance record to verify permissions
    const { data: attendanceRecord, error: fetchError } = await supabase
      .from('attendance')
      .select(`
        *,
        class:classes(school_id)
      `)
      .eq('id', attendanceId)
      .single()

    if (fetchError || !attendanceRecord) {
      return { error: 'Attendance record not found' }
    }

    // Verify user has permission to update this record
    const { data: adminData } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    const { data: teacherData } = await supabase
      .from('teachers')
      .select('id, school_id')
      .eq('user_id', user.id)
      .single()

    const userSchoolId = adminData?.school_id || teacherData?.school_id

    if (!userSchoolId || userSchoolId !== attendanceRecord.class.school_id) {
      return { error: 'Unauthorized: Cannot update this attendance record' }
    }

    // If user is teacher, verify they can update this class
    if (teacherData && !adminData) {
      const { data: teacherClass } = await supabase
        .from('teacher_classes')
        .select('id')
        .eq('teacher_id', teacherData.id)
        .eq('class_id', attendanceRecord.class_id)
        .single()

      if (!teacherClass) {
        return { error: 'Unauthorized: You cannot update attendance for this class' }
      }
    }

    // Update the attendance record
    const { error: updateError } = await supabase
      .from('attendance')
      .update({
        status,
        notes,
        updated_at: new Date().toISOString()
      })
      .eq('id', attendanceId)

    if (updateError) {
      console.error('Error updating attendance:', updateError)
      return { error: 'Failed to update attendance record' }
    }

    // Revalidate the attendance page
    revalidatePath('/attendance')

    return { 
      success: true, 
      message: 'Attendance record updated successfully' 
    }

  } catch (error) {
    console.error('Error in updateAttendanceRecord:', error)
    return { error: 'An unexpected error occurred' }
  }
}

export async function getAttendanceStats(
  classId?: string,
  startDate?: string,
  endDate?: string
): Promise<{
  success?: boolean
  error?: string
  stats?: {
    totalStudents: number
    presentCount: number
    absentCount: number
    lateCount: number
    excusedCount: number
    attendanceRate: number
  }
}> {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return { error: 'User not authenticated' }
    }

    // Verify user permissions
    const { data: adminData } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    const { data: teacherData } = await supabase
      .from('teachers')
      .select('id, school_id')
      .eq('user_id', user.id)
      .single()

    if (!adminData && !teacherData) {
      return { error: 'Unauthorized: User is not an admin or teacher' }
    }

    const schoolId = adminData?.school_id || teacherData?.school_id
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const end = endDate || new Date().toISOString().split('T')[0]

    // Build query
    let query = supabase
      .from('attendance')
      .select('status, student_id')
      .eq('school_id', schoolId)
      .gte('attendance_date', start)
      .lte('attendance_date', end)

    // If specific class requested and user is teacher, verify permission
    if (classId) {
      if (teacherData && !adminData) {
        const { data: teacherClass } = await supabase
          .from('teacher_classes')
          .select('id')
          .eq('teacher_id', teacherData.id)
          .eq('class_id', classId)
          .single()

        if (!teacherClass) {
          return { error: 'Unauthorized: You cannot view stats for this class' }
        }
      }
      query = query.eq('class_id', classId)
    } else if (teacherData && !adminData) {
      // If teacher and no specific class, only show their classes
      const { data: teacherClasses } = await supabase
        .from('teacher_classes')
        .select('class_id')
        .eq('teacher_id', teacherData.id)

      const classIds = teacherClasses?.map(tc => tc.class_id) || []
      if (classIds.length > 0) {
        query = query.in('class_id', classIds)
      } else {
        // Teacher has no classes
        return {
          success: true,
          stats: {
            totalStudents: 0,
            presentCount: 0,
            absentCount: 0,
            lateCount: 0,
            excusedCount: 0,
            attendanceRate: 0
          }
        }
      }
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching attendance stats:', error)
      return { error: 'Failed to fetch attendance statistics' }
    }

    // Calculate statistics
    const totalRecords = data?.length || 0
    const uniqueStudents = new Set(data?.map(record => record.student_id)).size
    const presentCount = data?.filter(record => record.status === 'present').length || 0
    const absentCount = data?.filter(record => record.status === 'absent').length || 0
    const lateCount = data?.filter(record => record.status === 'late').length || 0
    const excusedCount = data?.filter(record => record.status === 'excused').length || 0

    const stats = {
      totalStudents: uniqueStudents,
      presentCount,
      absentCount,
      lateCount,
      excusedCount,
      attendanceRate: totalRecords > 0 ? (presentCount / totalRecords) * 100 : 0
    }

    return { success: true, stats }

  } catch (error) {
    console.error('Error in getAttendanceStats:', error)
    return { error: 'An unexpected error occurred' }
  }
}
