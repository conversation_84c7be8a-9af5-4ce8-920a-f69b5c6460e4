'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'

export async function loginWithResult(formData: FormData) {
  const supabase = await createClient()

  // Extract and validate form data
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  // Basic validation
  if (!email || !password) {
    return { error: 'Email and password are required' }
  }

  if (!email.includes('@')) {
    return { error: 'Please enter a valid email address' }
  }

  console.log('Attempting login for email:', email)
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email: email.trim(),
    password,
  })

  console.log('Login response:', { 
    hasData: !!data, 
    hasUser: !!data?.user, 
    hasSession: !!data?.session,
    error: error?.message 
  })

  if (error) {
    console.error('Login error:', error)
    let errorMessage = 'Login failed. Please try again.'
    
    if (error.message.includes('Invalid login credentials')) {
      errorMessage = 'Invalid email or password. Please check your credentials and try again.'
    } else if (error.message.includes('<PERSON>ail not confirmed')) {
      errorMessage = 'Please check your email and click the confirmation link before signing in.'
    } else if (error.message.includes('Too many requests')) {
      errorMessage = 'Too many login attempts. Please wait a moment and try again.'
    } else {
      errorMessage = `Login failed: ${error.message}`
    }
    
    return { error: errorMessage }
  }

  if (!data?.user) {
    console.error('No user data received after successful auth')
    return { error: 'Login failed. No user data received.' }
  }

  console.log('Login successful for user:', data.user.email)

  // Revalidate all relevant paths
  revalidatePath('/', 'layout')
  revalidatePath('/dashboard', 'page')
  revalidatePath('/dashboard')

  return { success: true, user: data.user }
}

export async function login(formData: FormData) {
  const result = await loginWithResult(formData)
  
  if (result.error) {
    redirect('/error?message=' + encodeURIComponent(result.error))
  }
  
  if (result.success) {
    redirect('/dashboard')
  }
  
  // Fallback
  redirect('/error?message=' + encodeURIComponent('An unexpected error occurred'))
}
