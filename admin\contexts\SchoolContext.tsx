'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './AuthContext'
import { getCurrentUserSchool, School } from '@/lib/supabase'
import { createClient } from '@/utils/supabase/client'

interface SchoolContextType {
  school: School | null
  loading: boolean
  refreshSchool: () => Promise<void>
}

const SchoolContext = createContext<SchoolContextType | undefined>(undefined)

export function SchoolProvider({ children }: { children: React.ReactNode }) {
  const [school, setSchool] = useState<School | null>(null)
  const [loading, setLoading] = useState(true)
  const { user } = useAuth()
  const supabase = createClient()

  const refreshSchool = async () => {
    if (!user) {
      setSchool(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)

      // Try server-side API endpoint first
      const response = await fetch('/api/user-school', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.school) {
          setSchool(data.school)
          setLoading(false)
          return
        }
      }

      // Fallback: Set a default school for now to unblock development
      console.log('Using fallback school data')
      setSchool({
        id: '7e17b2cb-c0ac-43ed-b3a4-1956c2bee39a',
        name: 'Test High School',
        email: '<EMAIL>',
        phone: null,
        address: null,
        website: null,
        logo_url: null,
        subscription_plan: 'basic',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error fetching school:', error)
      // Use fallback school
      setSchool({
        id: '7e17b2cb-c0ac-43ed-b3a4-1956c2bee39a',
        name: 'Test High School',
        email: '<EMAIL>',
        phone: null,
        address: null,
        website: null,
        logo_url: null,
        subscription_plan: 'basic',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    refreshSchool()
  }, [user])

  const value = {
    school,
    loading,
    refreshSchool,
  }

  return (
    <SchoolContext.Provider value={value}>
      {children}
    </SchoolContext.Provider>
  )
}

export function useSchool() {
  const context = useContext(SchoolContext)
  if (context === undefined) {
    throw new Error('useSchool must be used within a SchoolProvider')
  }
  return context
}
