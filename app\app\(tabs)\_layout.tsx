import { Tabs } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { getEnvironmentConfig } from '@/config/appConfig';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSessionMonitor } from '@/hooks/useSessionMonitor';
import { useAuthStore } from '@/stores/authStore';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const { user } = useAuthStore();
  const config = getEnvironmentConfig();

  // Enable session monitoring for authenticated users with optimized intervals
  useSessionMonitor({
    checkInterval: config.SESSION.CHECK_INTERVAL,
    enableNetworkMonitoring: true,
    enableAppStateMonitoring: true,
  });

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: 'absolute',
          },
          default: {},
        }),
      }}>

      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
        }}
      />

      <Tabs.Screen
        name="student-profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.fill" color={color} />,
          href: user?.role === 'student' ? '/student-profile' : null,
        }}
      />

      <Tabs.Screen
        name="teacher-dashboard"
        options={{
          title: 'Students',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.3.fill" color={color} />,
          href: user?.role === 'teacher' ? '/teacher-dashboard' : null,
        }}
      />

      <Tabs.Screen
        name="teacher-classes"
        options={{
          title: 'Classes',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="book.fill" color={color} />,
          href: user?.role === 'teacher' ? '/teacher-classes' : null,
        }}
      />

      <Tabs.Screen
        name="teacher-profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.fill" color={color} />,
          href: user?.role === 'teacher' ? '/teacher-profile' : null,
        }}
      />

      <Tabs.Screen
        name="explore"
        options={{
          title: 'Explore',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="paperplane.fill" color={color} />,
          href: !user ? '/explore' : null,
        }}
      />
    </Tabs>
  );
}
