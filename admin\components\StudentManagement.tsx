'use client'

import React, { useState, useEffect } from 'react'
import { useStudentStore } from '@/stores/studentStore'
import { useClassStore } from '@/stores/classStore'
import { Student, Class } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { PhoneInput } from '@/components/ui/phone-input'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { enrollStudentInClass } from '@/app/(admin)/students/class-actions'
import { Trash2, Edit, Plus, AlertCircle, Loader2, GraduationCap, User, Mail, Phone, MapPin, Calendar, Copy, CheckCircle, UserPlus } from 'lucide-react'

// Grade options for the dropdown
const gradeOptions = [
  { value: 'Pre-K', label: 'Pre-Kindergarten' },
  { value: 'K', label: 'Kindergarten' },
  { value: '1st', label: '1st Grade' },
  { value: '2nd', label: '2nd Grade' },
  { value: '3rd', label: '3rd Grade' },
  { value: '4th', label: '4th Grade' },
  { value: '5th', label: '5th Grade' },
  { value: '6th', label: '6th Grade' },
  { value: '7th', label: '7th Grade' },
  { value: '8th', label: '8th Grade' },
  { value: '9th', label: '9th Grade' },
  { value: '10th', label: '10th Grade' },
  { value: '11th', label: '11th Grade' },
  { value: '12th', label: '12th Grade' },
]

interface StudentFormData {
  name: string
  email: string
  grade: string
  classId?: string
  parentName: string
  parentMobile: string
  address: string
  dateOfBirth: string
}

const StudentManagement: React.FC = () => {
  const {
    students,
    loading,
    error,
    fetchStudents,
    addStudent,
    updateStudent,
    deleteStudent,
    clearError
  } = useStudentStore()

  const {
    classes,
    fetchClasses
  } = useClassStore()

  const [form, setForm] = useState<StudentFormData>({
    name: '',
    email: '',
    grade: '',
    classId: '',
    parentName: '',
    parentMobile: '',
    address: '',
    dateOfBirth: ''
  })
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editForm, setEditForm] = useState<StudentFormData>({
    name: '',
    email: '',
    grade: '',
    classId: '',
    parentName: '',
    parentMobile: '',
    address: '',
    dateOfBirth: ''
  })
  const [newStudentCredentials, setNewStudentCredentials] = useState<{
    studentId: string;
    temporaryPassword: string;
    message: string;
  } | null>(null)
  const [copiedField, setCopiedField] = useState<string | null>(null)
  const [enrollingStudent, setEnrollingStudent] = useState<string | null>(null)
  const [selectedClassForEnrollment, setSelectedClassForEnrollment] = useState<string>('')

  useEffect(() => {
    fetchStudents()
    fetchClasses()
  }, [fetchStudents, fetchClasses])

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedField(field)
      setTimeout(() => setCopiedField(null), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const handleAdd = async (e: React.FormEvent) => {
    e.preventDefault()

    // Prevent submission if form is empty (this might be triggered on page refresh)
    if (!form.name.trim() || !form.email.trim() || !form.grade.trim() ||
        !form.parentName.trim() || !form.parentMobile.trim() ||
        !form.address.trim() || !form.dateOfBirth.trim()) {
      console.log('Form validation failed - missing required fields')
      return
    }

    console.log('Adding student:', form)
    try {
      const result = await addStudent(form)
      if (!error && result) {
        // Show the generated credentials to admin
        setNewStudentCredentials({
          studentId: result.studentId,
          temporaryPassword: result.temporaryPassword,
          message: result.message
        })

        // Clear the form
        setForm({
          name: '',
          email: '',
          grade: '',
          classId: '',
          parentName: '',
          parentMobile: '',
          address: '',
          dateOfBirth: ''
        })
      }
    } catch (err) {
      console.error('Error adding student:', err)
    }
  }

  const handleEdit = (student: Student) => {
    setEditingId(student.id)
    setEditForm({
      name: student.name,
      email: student.email,
      grade: student.grade,
      classId: '', // Will need to fetch student's current class
      parentName: student.parent_name,
      parentMobile: student.parent_mobile,
      address: student.address,
      dateOfBirth: student.date_of_birth
    })
  }

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingId) return
    
    await updateStudent(editingId, editForm)
    if (!error) {
      setEditingId(null)
      setEditForm({
        name: '',
        email: '',
        grade: '',
        classId: '',
        parentName: '',
        parentMobile: '',
        address: '',
        dateOfBirth: ''
      })
    }
  }

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this student?')) {
      await deleteStudent(id)
    }
  }

  const cancelEdit = () => {
    setEditingId(null)
    setEditForm({
      name: '',
      email: '',
      grade: '',
      classId: '',
      parentName: '',
      parentMobile: '',
      address: '',
      dateOfBirth: ''
    })
  }

  const handleEnrollStudent = async (studentId: string) => {
    if (!selectedClassForEnrollment) {
      alert('Please select a class first')
      return
    }

    try {
      const result = await enrollStudentInClass(studentId, selectedClassForEnrollment)

      if (result.error) {
        alert(`Error: ${result.error}`)
      } else {
        alert(result.message || 'Student enrolled successfully!')
        setEnrollingStudent(null)
        setSelectedClassForEnrollment('')
        // Refresh students list to show updated enrollment
        fetchStudents()
      }
    } catch (error) {
      console.error('Error enrolling student:', error)
      alert('Failed to enroll student')
    }
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Add Student Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add New Student
          </CardTitle>
          <CardDescription>
            Enter student information to create a new student record
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleAdd} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label htmlFor="name" className="block text-sm font-medium">
                  <User className="inline h-4 w-4 mr-1" />
                  Full Name *
                </label>
                <Input
                  id="name"
                  type="text"
                  placeholder="Enter student name"
                  value={form.name}
                  onChange={(e) => setForm(prev => ({ ...prev, name: e.target.value }))}
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium">
                  <Mail className="inline h-4 w-4 mr-1" />
                  Email Address *
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter email address"
                  value={form.email}
                  onChange={(e) => setForm(prev => ({ ...prev, email: e.target.value }))}
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="class" className="block text-sm font-medium">
                  <GraduationCap className="inline h-4 w-4 mr-1" />
                  Class *
                </label>
                <Select
                  value={form.classId}
                  onValueChange={(value) => {
                    const selectedClass = classes.find(c => c.id === value)
                    setForm(prev => ({
                      ...prev,
                      classId: value,
                      grade: selectedClass?.grade || ''
                    }))
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select class" />
                  </SelectTrigger>
                  <SelectContent>
                    {classes.map((classItem) => (
                      <SelectItem key={classItem.id} value={classItem.id}>
                        {classItem.class_name} - Grade {classItem.grade}
                        {classItem.section && ` (${classItem.section})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label htmlFor="parentName" className="block text-sm font-medium">
                  <User className="inline h-4 w-4 mr-1" />
                  Parent Name *
                </label>
                <Input
                  id="parentName"
                  type="text"
                  placeholder="Enter parent name"
                  value={form.parentName}
                  onChange={(e) => setForm(prev => ({ ...prev, parentName: e.target.value }))}
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="parentMobile" className="block text-sm font-medium">
                  <Phone className="inline h-4 w-4 mr-1" />
                  Parent Mobile No. *
                </label>
                <PhoneInput
                  id="parentMobile"
                  value={form.parentMobile}
                  onChange={(value) => setForm(prev => ({ ...prev, parentMobile: value }))}
                  placeholder="Enter parent mobile number"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label htmlFor="address" className="block text-sm font-medium">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Address *
                </label>
                <Input
                  id="address"
                  type="text"
                  placeholder="Enter student address"
                  value={form.address}
                  onChange={(e) => setForm(prev => ({ ...prev, address: e.target.value }))}
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="dateOfBirth" className="block text-sm font-medium">
                  <Calendar className="inline h-4 w-4 mr-1" />
                  Date of Birth *
                </label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={form.dateOfBirth}
                  onChange={(e) => setForm(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                  required
                />
              </div>
            </div>

            <Button type="submit" disabled={loading} className="w-full md:w-auto">
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding Student...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Student
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Students Table */}
      <Card>
        <CardHeader>
          <CardTitle>Student Records</CardTitle>
          <CardDescription>
            {students.length} student{students.length !== 1 ? 's' : ''} registered
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading && students.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading students...</span>
            </div>
          ) : students.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No students found. Add your first student above.
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Grade</TableHead>
                    <TableHead>Parent Name</TableHead>
                    <TableHead>Parent Mobile</TableHead>
                    <TableHead>Address</TableHead>
                    <TableHead>Date of Birth</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {students.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>
                        {editingId === student.id ? (
                          <Input
                            value={editForm.name}
                            onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                            className="w-full"
                          />
                        ) : (
                          <div className="font-medium">{student.name}</div>
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === student.id ? (
                          <Input
                            type="email"
                            value={editForm.email}
                            onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                            className="w-full"
                          />
                        ) : (
                          student.email
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === student.id ? (
                          <Select
                            value={editForm.grade}
                            onValueChange={(value) => setEditForm({ ...editForm, grade: value })}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select grade" />
                            </SelectTrigger>
                            <SelectContent>
                              {gradeOptions.map((grade) => (
                                <SelectItem key={grade.value} value={grade.value}>
                                  {grade.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          student.grade
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === student.id ? (
                          <Input
                            value={editForm.parentName}
                            onChange={(e) => setEditForm({ ...editForm, parentName: e.target.value })}
                            className="w-full"
                          />
                        ) : (
                          student.parent_name
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === student.id ? (
                          <PhoneInput
                            value={editForm.parentMobile}
                            onChange={(value) => setEditForm({ ...editForm, parentMobile: value })}
                            className="w-full"
                          />
                        ) : (
                          student.parent_mobile
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === student.id ? (
                          <Input
                            value={editForm.address}
                            onChange={(e) => setEditForm({ ...editForm, address: e.target.value })}
                            className="w-full"
                          />
                        ) : (
                          student.address
                        )}
                      </TableCell>
                      <TableCell>
                        {editingId === student.id ? (
                          <Input
                            type="date"
                            value={editForm.dateOfBirth}
                            onChange={(e) => setEditForm({ ...editForm, dateOfBirth: e.target.value })}
                            className="w-full"
                          />
                        ) : (
                          new Date(student.date_of_birth).toLocaleDateString()
                        )}
                      </TableCell>
                      <TableCell>
                        {new Date(student.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          {editingId === student.id ? (
                            <>
                              <Button
                                type="button"
                                size="sm"
                                onClick={handleUpdate}
                                disabled={loading}
                              >
                                Save
                              </Button>
                              <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={cancelEdit}
                              >
                                Cancel
                              </Button>
                            </>
                          ) : (
                            <>
                              <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={() => setEnrollingStudent(student.id)}
                                title="Enroll in Class"
                              >
                                <UserPlus className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                size="sm"
                                variant="outline"
                                onClick={() => handleEdit(student)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                size="sm"
                                variant="destructive"
                                onClick={() => handleDelete(student.id)}
                                disabled={loading}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Student Credentials Modal */}
      <Dialog open={!!newStudentCredentials} onOpenChange={() => setNewStudentCredentials(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Student Registered Successfully!
            </DialogTitle>
            <DialogDescription>
              Please share these credentials with the student. They should change their password on first login.
            </DialogDescription>
          </DialogHeader>

          {newStudentCredentials && (
            <div className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <p className="text-sm text-green-800 mb-3">{newStudentCredentials.message}</p>

                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Student ID:</label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                        {newStudentCredentials.studentId}
                      </code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(newStudentCredentials.studentId, 'studentId')}
                      >
                        {copiedField === 'studentId' ? (
                          <CheckCircle className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-700">Temporary Password:</label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                        {newStudentCredentials.temporaryPassword}
                      </code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(newStudentCredentials.temporaryPassword, 'password')}
                      >
                        {copiedField === 'password' ? (
                          <CheckCircle className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-xs text-yellow-800">
                    <strong>Important:</strong> This password is temporary and encrypted in our database.
                    The student should change it immediately after first login in the student app.
                  </p>
                </div>
              </div>

              <Button
                onClick={() => setNewStudentCredentials(null)}
                className="w-full"
              >
                Got it, thanks!
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Enroll Student in Class Dialog */}
      <Dialog open={!!enrollingStudent} onOpenChange={() => {
        setEnrollingStudent(null)
        setSelectedClassForEnrollment('')
      }}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5" />
              Enroll Student in Class
            </DialogTitle>
            <DialogDescription>
              Select a class to enroll this student in.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Select Class</label>
              <Select
                value={selectedClassForEnrollment}
                onValueChange={setSelectedClassForEnrollment}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a class" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map((classItem) => (
                    <SelectItem key={classItem.id} value={classItem.id}>
                      {classItem.class_name} - Grade {classItem.grade}
                      {classItem.section && ` (${classItem.section})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={() => enrollingStudent && handleEnrollStudent(enrollingStudent)}
                disabled={!selectedClassForEnrollment}
                className="flex-1"
              >
                Enroll Student
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setEnrollingStudent(null)
                  setSelectedClassForEnrollment('')
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default StudentManagement
