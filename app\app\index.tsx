import LoadingScreen from '@/components/LoadingScreen';
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert } from 'react-native';

export default function Index() {
  const router = useRouter();
  const { user, isLoading, isInitialized, error, initializeAuth, clearError } = useAuthStore();
  const [hasNavigated, setHasNavigated] = useState(false);

  useEffect(() => {
    // Initialize authentication on app startup
    initializeAuth();
  }, []);

  useEffect(() => {
    // Handle navigation based on auth state
    if (isInitialized && !hasNavigated) {
      // Add a small delay to ensure proper mounting
      const timer = setTimeout(() => {
        if (user) {
          console.log('User authenticated, navigating to tabs:', user.role);
          router.replace('/(tabs)');
          setHasNavigated(true);
        } else {
          console.log('No user found, navigating to sign-in');
          router.replace('/(auth)/sign-in');
          setHasNavigated(true);
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [user, isInitialized, hasNavigated]);

  useEffect(() => {
    // Handle authentication errors
    if (error && isInitialized) {
      console.error('Authentication error:', error);
      Alert.alert(
        'Authentication Error',
        'There was a problem with your session. Please sign in again.',
        [
          {
            text: 'OK',
            onPress: () => {
              clearError();
              if (!hasNavigated) {
                setTimeout(() => {
                  router.replace('/(auth)/sign-in');
                  setHasNavigated(true);
                }, 100);
              }
            },
          },
        ]
      );
    }
  }, [error, isInitialized]);

  // Show loading screen while initializing
  if (!isInitialized || isLoading) {
    return (
      <LoadingScreen
        message={
          !isInitialized
            ? "Initializing app..."
            : "Checking authentication..."
        }
      />
    );
  }

  // Fallback loading screen
  return <LoadingScreen message="Loading..." />;
}
