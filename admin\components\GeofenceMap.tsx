'use client'

import React, { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Circle, Marker, useMapEvents, useMap } from 'react-leaflet'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, Crosshair } from 'lucide-react'

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

interface GeofenceData {
  id?: string
  name: string
  center_latitude: number
  center_longitude: number
  radius_meters: number
  is_active: boolean
}

interface GeofenceMapProps {
  geofences: GeofenceData[]
  selectedGeofence?: GeofenceData | null
  onLocationSelect: (lat: number, lng: number) => void
  onGeofenceSelect?: (geofence: GeofenceData | null) => void
  editMode?: boolean
  height?: string
}

// Component to handle map clicks
function MapClickHandler({ onLocationSelect, editMode }: { onLocationSelect: (lat: number, lng: number) => void, editMode?: boolean }) {
  useMapEvents({
    click: (e) => {
      if (editMode) {
        onLocationSelect(e.latlng.lat, e.latlng.lng)
      }
    }
  })
  return null
}

// Component to handle map centering
function MapCenter({ center }: { center: [number, number] }) {
  const map = useMap()
  
  useEffect(() => {
    map.setView(center, map.getZoom())
  }, [center, map])
  
  return null
}

export default function GeofenceMap({
  geofences,
  selectedGeofence,
  onLocationSelect,
  onGeofenceSelect,
  editMode = false,
  height = '400px'
}: GeofenceMapProps) {
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [mapCenter, setMapCenter] = useState<[number, number]>([28.6139, 77.2090]) // Default to Delhi
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (selectedGeofence) {
      setMapCenter([selectedGeofence.center_latitude, selectedGeofence.center_longitude])
    } else if (geofences.length > 0) {
      setMapCenter([geofences[0].center_latitude, geofences[0].center_longitude])
    }
  }, [selectedGeofence, geofences])

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude
          const lng = position.coords.longitude
          setUserLocation([lat, lng])
          setMapCenter([lat, lng])
          if (editMode) {
            onLocationSelect(lat, lng)
          }
        },
        (error) => {
          console.error('Error getting location:', error)
          alert('Unable to get your current location. Please ensure location permissions are enabled.')
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      )
    } else {
      alert('Geolocation is not supported by this browser.')
    }
  }

  const handleGeofenceClick = (geofence: GeofenceData) => {
    if (onGeofenceSelect) {
      onGeofenceSelect(geofence)
    }
    setMapCenter([geofence.center_latitude, geofence.center_longitude])
  }

  if (!isClient) {
    return (
      <div 
        className="bg-gray-100 rounded-lg flex items-center justify-center"
        style={{ height }}
      >
        <div className="text-gray-500">Loading map...</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MapPin className="h-5 w-5 text-blue-600" />
          <span className="font-medium">School Location Map</span>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={getCurrentLocation}
          className="flex items-center gap-2"
        >
          <Crosshair className="h-4 w-4" />
          Use Current Location
        </Button>
      </div>

      <div 
        className="rounded-lg overflow-hidden border border-gray-200"
        style={{ height }}
      >
        <MapContainer
          center={mapCenter}
          zoom={15}
          style={{ height: '100%', width: '100%' }}
          scrollWheelZoom={true}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          <MapCenter center={mapCenter} />
          <MapClickHandler onLocationSelect={onLocationSelect} editMode={editMode} />

          {/* Render existing geofences */}
          {geofences.map((geofence) => (
            <React.Fragment key={geofence.id || `${geofence.center_latitude}-${geofence.center_longitude}`}>
              <Marker
                position={[geofence.center_latitude, geofence.center_longitude]}
                eventHandlers={{
                  click: () => handleGeofenceClick(geofence)
                }}
              />
              <Circle
                center={[geofence.center_latitude, geofence.center_longitude]}
                radius={geofence.radius_meters}
                pathOptions={{
                  color: geofence.is_active ? '#3b82f6' : '#6b7280',
                  fillColor: geofence.is_active ? '#3b82f6' : '#6b7280',
                  fillOpacity: 0.2,
                  weight: 2
                }}
                eventHandlers={{
                  click: () => handleGeofenceClick(geofence)
                }}
              />
            </React.Fragment>
          ))}

          {/* Show user's current location if available */}
          {userLocation && (
            <Marker
              position={userLocation}
              icon={L.icon({
                iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
                shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34],
                shadowSize: [41, 41]
              })}
            />
          )}

          {/* Show selected location in edit mode */}
          {editMode && selectedGeofence && (
            <>
              <Marker
                position={[selectedGeofence.center_latitude, selectedGeofence.center_longitude]}
                icon={L.icon({
                  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-green.png',
                  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
                  iconSize: [25, 41],
                  iconAnchor: [12, 41],
                  popupAnchor: [1, -34],
                  shadowSize: [41, 41]
                })}
              />
              <Circle
                center={[selectedGeofence.center_latitude, selectedGeofence.center_longitude]}
                radius={selectedGeofence.radius_meters}
                pathOptions={{
                  color: '#10b981',
                  fillColor: '#10b981',
                  fillOpacity: 0.3,
                  weight: 3
                }}
              />
            </>
          )}
        </MapContainer>
      </div>

      {editMode && (
        <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-blue-600" />
            <span className="font-medium">Edit Mode:</span>
          </div>
          <ul className="mt-2 space-y-1 text-xs">
            <li>• Click on the map to set a new location</li>
            <li>• Use "Current Location" to set your current position</li>
            <li>• Green marker shows the selected location</li>
            <li>• Blue circles show existing geofences</li>
          </ul>
        </div>
      )}
    </div>
  )
}
