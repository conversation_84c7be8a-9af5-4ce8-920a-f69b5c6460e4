'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/utils/supabase/server'
import { createServiceClient } from '@/utils/supabase/service'
import { generateTeacherPassword, generateTeacherId, hashPassword } from '@/lib/password-utils'

export interface TeacherRegistrationData {
  name: string
  email: string
  phone: string
  subject: string
  department: string
  teacherId?: string // Optional - will be auto-generated if not provided
}

export interface TeacherRegistrationResult {
  success?: boolean
  error?: string
  teacherId?: string
  temporaryPassword?: string
  message?: string
}

export async function registerTeacher(formData: FormData): Promise<TeacherRegistrationResult> {
  try {
    const supabase = await createClient()
    
    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return { error: 'Unauthorized. Please log in as an admin.' }
    }

    // Get admin's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return { error: 'Admin school not found. Please contact support.' }
    }

    // Extract form data
    const teacherData: TeacherRegistrationData = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      phone: formData.get('phone') as string,
      subject: formData.get('subject') as string,
      department: formData.get('department') as string,
      teacherId: formData.get('teacherId') as string
    }

    // Validate required fields
    if (!teacherData.name || !teacherData.email) {
      return { error: 'Name and email are required.' }
    }

    // Check if email already exists
    const { data: existingTeacher } = await supabase
      .from('teachers')
      .select('id')
      .eq('email', teacherData.email)
      .eq('school_id', schoolAdmin.school_id)
      .single()

    if (existingTeacher) {
      return { error: 'A teacher with this email already exists.' }
    }

    // Generate teacher credentials
    const temporaryPassword = generateTeacherPassword(teacherData.name)
    const autoGeneratedTeacherId = teacherData.teacherId || generateTeacherId(teacherData.name)
    
    console.log('Generated teacher credentials:', { 
      teacherId: autoGeneratedTeacherId, 
      temporaryPassword: temporaryPassword.substring(0, 3) + '***' 
    })

    // Check if teacher ID already exists
    const { data: existingTeacherId } = await supabase
      .from('teachers')
      .select('id')
      .eq('teacher_id', autoGeneratedTeacherId)
      .eq('school_id', schoolAdmin.school_id)
      .single()

    if (existingTeacherId) {
      return { error: 'Teacher ID already exists. Please use a different ID.' }
    }

    // Create teacher user account using service client (has admin privileges)
    const serviceSupabase = createServiceClient()
    const { data: authData, error: authCreateError } = await serviceSupabase.auth.admin.createUser({
      email: teacherData.email,
      password: temporaryPassword,
      email_confirm: true, // Auto-confirm email for teachers
      user_metadata: {
        name: teacherData.name,
        role: 'teacher',
        teacher_id: autoGeneratedTeacherId,
        school_id: schoolAdmin.school_id
      }
    })

    if (authCreateError) {
      console.error('Error creating teacher auth user:', authCreateError)
      return { error: `Failed to create teacher account: ${authCreateError.message}` }
    }

    if (!authData.user) {
      return { error: 'Failed to create teacher account. No user data returned.' }
    }

    // Hash the password for storage in our database
    const hashedPassword = await hashPassword(temporaryPassword)

    // Insert teacher record into teachers table
    const { error: insertError } = await supabase
      .from('teachers')
      .insert([{
        user_id: authData.user.id,
        teacher_id: autoGeneratedTeacherId,
        name: teacherData.name,
        email: teacherData.email,
        phone: teacherData.phone,
        subject: teacherData.subject,
        department: teacherData.department,
        school_id: schoolAdmin.school_id,
        role: 'teacher',
        is_active: true,
        temporary_password_hash: hashedPassword,
        password_changed: false,
        created_by: user.id
      }])

    if (insertError) {
      console.error('Error inserting teacher record:', insertError)
      
      // Clean up the auth user if database insert fails
      try {
        await serviceSupabase.auth.admin.deleteUser(authData.user.id)
      } catch (cleanupError) {
        console.error('Error cleaning up auth user:', cleanupError)
      }
      
      return { error: `Failed to create teacher record: ${insertError.message}` }
    }

    // Revalidate the teachers page
    revalidatePath('/teachers')

    return {
      success: true,
      teacherId: autoGeneratedTeacherId,
      temporaryPassword,
      message: `Teacher account created successfully! Teacher ID: ${autoGeneratedTeacherId}`
    }

  } catch (error) {
    console.error('Unexpected error in registerTeacher:', error)
    return { 
      error: error instanceof Error ? error.message : 'An unexpected error occurred while creating the teacher account.' 
    }
  }
}

export async function updateTeacher(teacherId: string, updates: Partial<TeacherRegistrationData>): Promise<{ success?: boolean; error?: string }> {
  try {
    const supabase = await createClient()
    
    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return { error: 'Unauthorized. Please log in as an admin.' }
    }

    // Get admin's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return { error: 'Admin school not found. Please contact support.' }
    }

    // Update teacher record
    const { error: updateError } = await supabase
      .from('teachers')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', teacherId)
      .eq('school_id', schoolAdmin.school_id)

    if (updateError) {
      console.error('Error updating teacher:', updateError)
      return { error: `Failed to update teacher: ${updateError.message}` }
    }

    // Revalidate the teachers page
    revalidatePath('/teachers')

    return { success: true }

  } catch (error) {
    console.error('Unexpected error in updateTeacher:', error)
    return { 
      error: error instanceof Error ? error.message : 'An unexpected error occurred while updating the teacher.' 
    }
  }
}

export async function deleteTeacher(teacherId: string): Promise<{ success?: boolean; error?: string }> {
  try {
    const supabase = await createClient()

    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return { error: 'Unauthorized. Please log in as an admin.' }
    }

    // Get admin's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return { error: 'Admin school not found. Please contact support.' }
    }

    // Get teacher data to delete auth user and check related records
    const { data: teacher, error: teacherError } = await supabase
      .from('teachers')
      .select('user_id, teacher_id, name')
      .eq('id', teacherId)
      .eq('school_id', schoolAdmin.school_id)
      .single()

    if (teacherError || !teacher) {
      return { error: 'Teacher not found.' }
    }

    console.log(`Starting deletion process for teacher: ${teacher.name} (ID: ${teacher.teacher_id})`)

    // Step 1: Delete all teacher-class assignments
    const { error: classAssignmentError } = await supabase
      .from('teacher_classes')
      .delete()
      .eq('teacher_id', teacherId)
      .eq('school_id', schoolAdmin.school_id)

    if (classAssignmentError) {
      console.error('Error deleting teacher class assignments:', classAssignmentError)
      return { error: `Failed to remove teacher class assignments: ${classAssignmentError.message}` }
    }

    // Step 2: Update attendance records to remove teacher reference (set marked_by to admin)
    // We don't delete attendance records as they are historical data
    const { error: attendanceUpdateError } = await supabase
      .from('attendance')
      .update({
        marked_by: user.id, // Transfer to current admin
        updated_at: new Date().toISOString()
      })
      .eq('marked_by', teacher.user_id || teacherId)

    if (attendanceUpdateError) {
      console.error('Error updating attendance records:', attendanceUpdateError)
      // Don't fail the deletion for this, just log it
    }

    // Step 3: Update attendance audit records
    const { error: auditUpdateError } = await supabase
      .from('attendance_audit')
      .update({
        changed_by: user.id, // Transfer to current admin
        reason: `Teacher ${teacher.name} was deleted, audit record transferred to admin`
      })
      .eq('changed_by', teacher.user_id || teacherId)

    if (auditUpdateError) {
      console.error('Error updating attendance audit records:', auditUpdateError)
      // Don't fail the deletion for this, just log it
    }

    // Step 4: Delete the teacher record
    const { error: deleteError } = await supabase
      .from('teachers')
      .delete()
      .eq('id', teacherId)
      .eq('school_id', schoolAdmin.school_id)

    if (deleteError) {
      console.error('Error deleting teacher record:', deleteError)
      return { error: `Failed to delete teacher: ${deleteError.message}` }
    }

    // Step 5: Delete auth user if exists
    if (teacher.user_id) {
      try {
        const serviceSupabase = createServiceClient()
        await serviceSupabase.auth.admin.deleteUser(teacher.user_id)
        console.log(`Successfully deleted auth user for teacher: ${teacher.name}`)
      } catch (authDeleteError) {
        console.error('Error deleting teacher auth user:', authDeleteError)
        // Don't return error here as the teacher record is already deleted
        // The auth user deletion is not critical for the main operation
      }
    }

    console.log(`Successfully completed deletion process for teacher: ${teacher.name}`)

    // Revalidate the teachers page
    revalidatePath('/teachers')

    return { success: true }

  } catch (error) {
    console.error('Unexpected error in deleteTeacher:', error)
    return {
      error: error instanceof Error ? error.message : 'An unexpected error occurred while deleting the teacher.'
    }
  }
}
