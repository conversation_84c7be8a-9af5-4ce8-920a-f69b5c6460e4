import { createClient } from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

// Custom storage implementation using Expo SecureStore
const ExpoSecureStoreAdapter = {
  getItem: (key: string) => {
    return SecureStore.getItemAsync(key);
  },
  setItem: (key: string, value: string) => {
    SecureStore.setItemAsync(key, value);
  },
  removeItem: (key: string) => {
    SecureStore.deleteItemAsync(key);
  },
};

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: ExpoSecureStoreAdapter,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types based on the schema
export interface Student {
  id: string;
  user_id?: string;
  name: string;
  email: string;
  grade?: string;
  role?: string;
  created_at?: string;
  parent_name?: string;
  parent_mobile?: string;
  address?: string;
  date_of_birth?: string;
  school_id?: string;
  student_id?: string;
  temporary_password_hash?: string;
  password_changed?: boolean;
  created_by?: string;
}

export interface Teacher {
  id: string;
  user_id?: string;
  school_id: string;
  teacher_id: string;
  name: string;
  email: string;
  phone?: string;
  subject?: string;
  department?: string;
  role?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  temporary_password_hash?: string;
  password_changed?: boolean;
}

export interface Class {
  id: string;
  school_id: string;
  class_name: string;
  grade: string;
  section?: string;
  subject?: string;
  academic_year: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Attendance {
  id: string;
  student_id: string;
  class_id: string;
  school_id: string;
  attendance_date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  marked_by: string;
  marked_at?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface TeacherAttendance {
  id: string;
  teacher_id: string;
  school_id: string;
  attendance_date: string;
  check_in_time?: string;
  check_out_time?: string;
  status: 'present' | 'absent' | 'late' | 'half_day' | 'sick_leave' | 'personal_leave';

  // Location verification data
  check_in_latitude?: number;
  check_in_longitude?: number;
  check_in_accuracy?: number;
  check_in_address?: string;
  check_out_latitude?: number;
  check_out_longitude?: number;
  check_out_accuracy?: number;
  check_out_address?: string;

  // Device and security information
  device_id?: string;
  device_type?: string;
  app_version?: string;
  ip_address?: string;
  user_agent?: string;

  // Verification and validation
  location_verified?: boolean;
  within_geofence?: boolean;
  manual_override?: boolean;
  override_reason?: string;
  override_by?: string;

  // Notes and additional information
  notes?: string;
  work_hours?: number;

  // Audit fields
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

export interface SchoolGeofence {
  id: string;
  school_id: string;
  name: string;
  center_latitude: number;
  center_longitude: number;
  radius_meters: number;
  is_active?: boolean;

  // Time restrictions
  allowed_check_in_start?: string;
  allowed_check_in_end?: string;
  allowed_check_out_start?: string;
  allowed_check_out_end?: string;

  // Days of week (0 = Sunday, 6 = Saturday)
  allowed_days?: number[];

  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface TeacherAttendanceAudit {
  id: string;
  attendance_id: string;
  action: string;
  old_values?: any;
  new_values?: any;

  // Security context
  ip_address?: string;
  user_agent?: string;
  device_id?: string;
  location_latitude?: number;
  location_longitude?: number;

  // Audit metadata
  performed_by?: string;
  performed_at?: string;
  reason?: string;
}

export interface TeacherAttendanceSuspiciousActivity {
  id: string;
  teacher_id: string;
  activity_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';

  // Activity details
  description: string;
  metadata?: any;

  // Location context
  attempted_latitude?: number;
  attempted_longitude?: number;
  expected_latitude?: number;
  expected_longitude?: number;
  distance_meters?: number;

  // Device context
  device_id?: string;
  ip_address?: string;
  user_agent?: string;

  // Resolution
  resolved?: boolean;
  resolved_by?: string;
  resolved_at?: string;
  resolution_notes?: string;

  created_at?: string;
}

export interface TeacherAttendanceSettings {
  id: string;
  school_id: string;

  // Time restrictions
  earliest_check_in?: string;
  latest_check_in?: string;
  earliest_check_out?: string;
  latest_check_out?: string;

  // Location settings
  require_location?: boolean;
  max_location_accuracy_meters?: number;
  geofence_buffer_meters?: number;

  // Security settings
  max_daily_attempts?: number;
  lockout_duration_minutes?: number;
  require_photo_verification?: boolean;
  allow_manual_override?: boolean;

  // Notification settings
  notify_admin_on_suspicious?: boolean;
  notify_teacher_on_location_fail?: boolean;

  // Work hours calculation
  minimum_work_hours?: number;
  break_time_minutes?: number;

  created_at?: string;
  updated_at?: string;
  updated_by?: string;
}

export interface Student {
  id: string;
  user_id?: string;
  name: string;
  email: string;
  grade?: string;
  class_id?: string;
  student_id?: string;
  phone?: string;
  address?: string;
  parent_contact?: string;
  parent_email?: string;
  medical_info?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  emergency_contact?: string;
  emergency_phone?: string;
  school_id: string;
  enrollment_date?: string;
  is_active?: boolean;
  profile_picture_url?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

export interface Class {
  id: string;
  class_name?: string; // Legacy field
  name?: string;
  subject: string;
  grade?: string; // Legacy field
  grade_level?: string;
  school_id: string;
  teacher_id?: string;
  academic_year?: string;
  semester?: string;
  schedule?: any; // JSONB
  room_number?: string;
  max_students?: number;
  description?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface StudentEnrollment {
  id: string;
  student_id: string;
  class_id: string;
  enrollment_date?: string;
  status?: 'active' | 'inactive' | 'transferred' | 'completed';
  final_grade?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  enrolled_by?: string;
}

export type UserRole = 'student' | 'teacher' | 'admin';

export interface AuthUser {
  id: string;
  email: string;
  role: UserRole;
  profile: Student | Teacher;
}
