import { UserRole } from '@/lib/supabase';
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { KeyboardAvoidingView, Platform, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function SignIn() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [signInError, setSignInError] = useState<string | null>(null);
  const { signIn, isLoading, error, user, clearError } = useAuthStore();
  const router = useRouter();

  const handleSignIn = async () => {
    if (!email || !password || !selectedRole) {
      setSignInError('Please fill in all fields and select your role');
      return;
    }

    // Clear any previous errors
    setSignInError(null);
    clearError();

    try {
      await signIn(email, password, selectedRole);
      // Navigation will be handled by useEffect when user state changes
    } catch (error: any) {
      // Error is already handled by the auth store
      // We'll display it in the UI via useEffect
      console.error('Sign in error:', error);
    }
  };

  // Handle successful authentication - navigate to tabs
  React.useEffect(() => {
    if (user && !isLoading) {
      console.log('User authenticated, navigating to tabs:', user.role);
      router.replace('/(tabs)');
    }
  }, [user, isLoading]);

  // Handle authentication errors - display error message
  React.useEffect(() => {
    if (error && !isLoading) {
      setSignInError(error);
      clearError(); // Clear the error from the store
    }
  }, [error, isLoading]);

  // Clear errors when user starts typing
  const handleEmailChange = (text: string) => {
    setEmail(text);
    if (signInError) {
      setSignInError(null);
    }
  };

  const handlePasswordChange = (text: string) => {
    setPassword(text);
    if (signInError) {
      setSignInError(null);
    }
  };

  const handleRoleSelection = (role: UserRole) => {
    setSelectedRole(role);
    if (signInError) {
      setSignInError(null);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-light-background dark:bg-dark-background"
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
        <View className="flex-1 justify-center px-6">
          {/* Header */}
          <View className="mb-8">
            <Text className="text-3xl font-rubik-bold text-center text-neutral-900 dark:text-neutral-100 mb-2">
              Welcome Back
            </Text>
            <Text className="text-base font-rubik text-center text-neutral-600 dark:text-neutral-400">
              Sign in to your school account
            </Text>
          </View>

          {/* Role Selection */}
          <View className="mb-6">
            <Text className="text-lg font-rubik-semibold text-neutral-800 dark:text-neutral-200 mb-3">
              I am a:
            </Text>
            <View className="flex-row gap-4">
              <TouchableOpacity
                onPress={() => handleRoleSelection('student')}
                className={`flex-1 py-4 px-6 rounded-xl border-2 ${
                  selectedRole === 'student'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800'
                }`}
              >
                <Text className={`text-center font-rubik-medium ${
                  selectedRole === 'student'
                    ? 'text-primary-700 dark:text-primary-300'
                    : 'text-neutral-700 dark:text-neutral-300'
                }`}>
                  Student
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => handleRoleSelection('teacher')}
                className={`flex-1 py-4 px-6 rounded-xl border-2 ${
                  selectedRole === 'teacher'
                    ? 'border-secondary-500 bg-secondary-50 dark:bg-secondary-900/20'
                    : 'border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800'
                }`}
              >
                <Text className={`text-center font-rubik-medium ${
                  selectedRole === 'teacher'
                    ? 'text-secondary-700 dark:text-secondary-300'
                    : 'text-neutral-700 dark:text-neutral-300'
                }`}>
                  Teacher
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Email Input */}
          <View className="mb-4">
            <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
              Email Address
            </Text>
            <TextInput
              value={email}
              onChangeText={handleEmailChange}
              placeholder="Enter your email"
              placeholderTextColor="#9CA3AF"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
            />
          </View>

          {/* Password Input */}
          <View className="mb-6">
            <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
              Password
            </Text>
            <TextInput
              value={password}
              onChangeText={handlePasswordChange}
              placeholder="Enter your password"
              placeholderTextColor="#9CA3AF"
              secureTextEntry
              className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
            />
          </View>

          {/* Error Message */}
          {signInError && (
            <View className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
              <Text className="text-red-700 dark:text-red-300 font-rubik text-center">
                {signInError}
              </Text>
            </View>
          )}

          {/* Sign In Button */}
          <TouchableOpacity
            onPress={handleSignIn}
            disabled={isLoading}
            className={`w-full py-4 rounded-xl ${
              isLoading
                ? 'bg-neutral-300 dark:bg-neutral-700'
                : selectedRole === 'student'
                ? 'bg-primary-500'
                : selectedRole === 'teacher'
                ? 'bg-secondary-500'
                : 'bg-neutral-400'
            }`}
          >
            <Text className="text-white text-center font-rubik-semibold text-lg">
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Text>
          </TouchableOpacity>

          {/* Help Text */}
          <View className="mt-6">
            <Text className="text-sm font-rubik text-center text-neutral-500 dark:text-neutral-400">
              Having trouble signing in? Contact your school administrator.
            </Text>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}