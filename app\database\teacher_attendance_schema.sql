-- Teacher Attendance Database Schema
-- This schema provides secure teacher attendance tracking with location verification,
-- time restrictions, fraud detection, and comprehensive audit logging.

-- =====================================================
-- 1. TEACHER ATTENDANCE TABLE
-- =====================================================

CREATE TABLE teacher_attendance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
    school_id UUID NOT NULL,
    attendance_date DATE NOT NULL DEFAULT CURRENT_DATE,
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) NOT NULL DEFAULT 'absent' CHECK (status IN ('present', 'absent', 'late', 'half_day', 'sick_leave', 'personal_leave')),
    
    -- Location verification data
    check_in_latitude DECIMAL(10, 8),
    check_in_longitude DECIMAL(11, 8),
    check_in_accuracy DECIMAL(8, 2), -- GPS accuracy in meters
    check_in_address TEXT,
    check_out_latitude DECIMAL(10, 8),
    check_out_longitude DECIMAL(11, 8),
    check_out_accuracy DECIMAL(8, 2),
    check_out_address TEXT,
    
    -- Device and security information
    device_id TEXT,
    device_type VARCHAR(50), -- 'ios', 'android', etc.
    app_version VARCHAR(20),
    ip_address INET,
    user_agent TEXT,
    
    -- Verification and validation
    location_verified BOOLEAN DEFAULT FALSE,
    within_geofence BOOLEAN DEFAULT FALSE,
    manual_override BOOLEAN DEFAULT FALSE,
    override_reason TEXT,
    override_by UUID REFERENCES teachers(id),
    
    -- Notes and additional information
    notes TEXT,
    work_hours DECIMAL(4, 2), -- Calculated work hours
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    UNIQUE(teacher_id, attendance_date),
    CHECK (check_out_time IS NULL OR check_out_time > check_in_time),
    CHECK (work_hours IS NULL OR work_hours >= 0),
    CHECK (check_in_accuracy IS NULL OR check_in_accuracy >= 0),
    CHECK (check_out_accuracy IS NULL OR check_out_accuracy >= 0)
);

-- =====================================================
-- 2. SCHOOL GEOFENCE CONFIGURATION
-- =====================================================

CREATE TABLE school_geofences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    school_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    center_latitude DECIMAL(10, 8) NOT NULL,
    center_longitude DECIMAL(11, 8) NOT NULL,
    radius_meters INTEGER NOT NULL CHECK (radius_meters > 0),
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Time restrictions
    allowed_check_in_start TIME,
    allowed_check_in_end TIME,
    allowed_check_out_start TIME,
    allowed_check_out_end TIME,
    
    -- Days of week (0 = Sunday, 6 = Saturday)
    allowed_days INTEGER[] DEFAULT '{1,2,3,4,5}', -- Monday to Friday
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id)
);

-- =====================================================
-- 3. ATTENDANCE AUDIT LOG
-- =====================================================

CREATE TABLE teacher_attendance_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    attendance_id UUID NOT NULL REFERENCES teacher_attendance(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL, -- 'created', 'updated', 'deleted', 'check_in', 'check_out', 'override'
    old_values JSONB,
    new_values JSONB,
    
    -- Security context
    ip_address INET,
    user_agent TEXT,
    device_id TEXT,
    location_latitude DECIMAL(10, 8),
    location_longitude DECIMAL(11, 8),
    
    -- Audit metadata
    performed_by UUID REFERENCES auth.users(id),
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    reason TEXT
);

-- =====================================================
-- 4. SUSPICIOUS ACTIVITY LOG
-- =====================================================

CREATE TABLE teacher_attendance_suspicious_activity (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    teacher_id UUID NOT NULL REFERENCES teachers(id),
    activity_type VARCHAR(50) NOT NULL, -- 'location_mismatch', 'time_violation', 'multiple_attempts', 'device_change'
    severity VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    
    -- Activity details
    description TEXT NOT NULL,
    metadata JSONB, -- Store additional context data
    
    -- Location context
    attempted_latitude DECIMAL(10, 8),
    attempted_longitude DECIMAL(11, 8),
    expected_latitude DECIMAL(10, 8),
    expected_longitude DECIMAL(11, 8),
    distance_meters DECIMAL(10, 2),
    
    -- Device context
    device_id TEXT,
    ip_address INET,
    user_agent TEXT,
    
    -- Resolution
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by UUID REFERENCES auth.users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 5. TEACHER ATTENDANCE SETTINGS
-- =====================================================

CREATE TABLE teacher_attendance_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    school_id UUID NOT NULL,
    
    -- Time restrictions
    earliest_check_in TIME DEFAULT '06:00:00',
    latest_check_in TIME DEFAULT '10:00:00',
    earliest_check_out TIME DEFAULT '14:00:00',
    latest_check_out TIME DEFAULT '20:00:00',
    
    -- Location settings
    require_location BOOLEAN DEFAULT TRUE,
    max_location_accuracy_meters INTEGER DEFAULT 50,
    geofence_buffer_meters INTEGER DEFAULT 100,
    
    -- Security settings
    max_daily_attempts INTEGER DEFAULT 5,
    lockout_duration_minutes INTEGER DEFAULT 30,
    require_photo_verification BOOLEAN DEFAULT FALSE,
    allow_manual_override BOOLEAN DEFAULT TRUE,
    
    -- Notification settings
    notify_admin_on_suspicious BOOLEAN DEFAULT TRUE,
    notify_teacher_on_location_fail BOOLEAN DEFAULT TRUE,
    
    -- Work hours calculation
    minimum_work_hours DECIMAL(4, 2) DEFAULT 8.0,
    break_time_minutes INTEGER DEFAULT 60,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID REFERENCES auth.users(id),
    
    UNIQUE(school_id)
);

-- =====================================================
-- 6. INDEXES FOR PERFORMANCE
-- =====================================================

-- Teacher attendance indexes
CREATE INDEX idx_teacher_attendance_teacher_date ON teacher_attendance(teacher_id, attendance_date);
CREATE INDEX idx_teacher_attendance_school_date ON teacher_attendance(school_id, attendance_date);
CREATE INDEX idx_teacher_attendance_status ON teacher_attendance(status);
CREATE INDEX idx_teacher_attendance_created_at ON teacher_attendance(created_at);
CREATE INDEX idx_teacher_attendance_location ON teacher_attendance(check_in_latitude, check_in_longitude);

-- Geofence indexes
CREATE INDEX idx_school_geofences_school_active ON school_geofences(school_id, is_active);
CREATE INDEX idx_school_geofences_location ON school_geofences(center_latitude, center_longitude);

-- Audit log indexes
CREATE INDEX idx_attendance_audit_attendance_id ON teacher_attendance_audit(attendance_id);
CREATE INDEX idx_attendance_audit_performed_at ON teacher_attendance_audit(performed_at);
CREATE INDEX idx_attendance_audit_action ON teacher_attendance_audit(action);

-- Suspicious activity indexes
CREATE INDEX idx_suspicious_activity_teacher_id ON teacher_attendance_suspicious_activity(teacher_id);
CREATE INDEX idx_suspicious_activity_created_at ON teacher_attendance_suspicious_activity(created_at);
CREATE INDEX idx_suspicious_activity_severity ON teacher_attendance_suspicious_activity(severity);
CREATE INDEX idx_suspicious_activity_resolved ON teacher_attendance_suspicious_activity(resolved);

-- =====================================================
-- 7. ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE teacher_attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE school_geofences ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_attendance_audit ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_attendance_suspicious_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_attendance_settings ENABLE ROW LEVEL SECURITY;

-- Teacher attendance policies
CREATE POLICY teacher_attendance_teacher_own ON teacher_attendance
    FOR ALL TO authenticated
    USING (
        teacher_id IN (
            SELECT id FROM teachers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY teacher_attendance_admin_all ON teacher_attendance
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teachers t 
            WHERE t.user_id = auth.uid() 
            AND t.role = 'admin'
        )
    );

-- Geofence policies (read-only for teachers, full access for admins)
CREATE POLICY school_geofences_teacher_read ON school_geofences
    FOR SELECT TO authenticated
    USING (
        school_id IN (
            SELECT school_id FROM teachers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY school_geofences_admin_all ON school_geofences
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teachers t 
            WHERE t.user_id = auth.uid() 
            AND t.role = 'admin'
        )
    );

-- Audit log policies (read-only for teachers of their own records, full access for admins)
CREATE POLICY attendance_audit_teacher_read ON teacher_attendance_audit
    FOR SELECT TO authenticated
    USING (
        attendance_id IN (
            SELECT id FROM teacher_attendance ta
            JOIN teachers t ON ta.teacher_id = t.id
            WHERE t.user_id = auth.uid()
        )
    );

CREATE POLICY attendance_audit_admin_all ON teacher_attendance_audit
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teachers t 
            WHERE t.user_id = auth.uid() 
            AND t.role = 'admin'
        )
    );

-- Suspicious activity policies (teachers can read their own, admins can read all)
CREATE POLICY suspicious_activity_teacher_read ON teacher_attendance_suspicious_activity
    FOR SELECT TO authenticated
    USING (
        teacher_id IN (
            SELECT id FROM teachers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY suspicious_activity_admin_all ON teacher_attendance_suspicious_activity
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teachers t 
            WHERE t.user_id = auth.uid() 
            AND t.role = 'admin'
        )
    );

-- Settings policies (read for teachers, full access for admins)
CREATE POLICY attendance_settings_teacher_read ON teacher_attendance_settings
    FOR SELECT TO authenticated
    USING (
        school_id IN (
            SELECT school_id FROM teachers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY attendance_settings_admin_all ON teacher_attendance_settings
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teachers t 
            WHERE t.user_id = auth.uid() 
            AND t.role = 'admin'
        )
    );

-- =====================================================
-- 8. TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers
CREATE TRIGGER update_teacher_attendance_updated_at 
    BEFORE UPDATE ON teacher_attendance 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_school_geofences_updated_at 
    BEFORE UPDATE ON school_geofences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_attendance_settings_updated_at 
    BEFORE UPDATE ON teacher_attendance_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 9. UTILITY FUNCTIONS
-- =====================================================

-- Function to calculate distance between two points (Haversine formula)
CREATE OR REPLACE FUNCTION calculate_distance_meters(
    lat1 DECIMAL(10, 8),
    lon1 DECIMAL(11, 8),
    lat2 DECIMAL(10, 8),
    lon2 DECIMAL(11, 8)
)
RETURNS DECIMAL(10, 2) AS $$
DECLARE
    earth_radius CONSTANT DECIMAL := 6371000; -- Earth radius in meters
    dlat DECIMAL;
    dlon DECIMAL;
    a DECIMAL;
    c DECIMAL;
BEGIN
    dlat := radians(lat2 - lat1);
    dlon := radians(lon2 - lon1);
    a := sin(dlat/2) * sin(dlat/2) + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2) * sin(dlon/2);
    c := 2 * atan2(sqrt(a), sqrt(1-a));
    RETURN earth_radius * c;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to check if a point is within a geofence
CREATE OR REPLACE FUNCTION is_within_geofence(
    check_lat DECIMAL(10, 8),
    check_lon DECIMAL(11, 8),
    center_lat DECIMAL(10, 8),
    center_lon DECIMAL(11, 8),
    radius_meters INTEGER
)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN calculate_distance_meters(check_lat, check_lon, center_lat, center_lon) <= radius_meters;
END;
$$ LANGUAGE plpgsql IMMUTABLE;
