# School Management Mobile App

This React Native Expo app provides mobile access for students and teachers in the school management system.

## Features Implemented

### Authentication System
- **Role-based sign-in**: Students and teachers can select their role and sign in with credentials provided by admin
- **Secure session management**: Uses Expo SecureStore for secure token storage
- **Password change functionality**: Users can change their temporary passwords from their profile

### Student Features
- **Profile Dashboard**: View personal information, grades, and attendance statistics
- **Attendance History**: View recent attendance records with status indicators
- **Quick Overview**: Dashboard showing current grade and attendance percentage
- **Password Management**: Change password from profile section

### Teacher Features
- **Teacher Dashboard**: Manage students, update grades, and mark attendance
- **Student Management**: View all students with ability to update grades and mark attendance
- **Class Management**: View assigned classes and manage class-specific attendance
- **Bulk Operations**: Efficiently manage multiple students

### Technical Features
- **Professional UI**: Clean, modern design using NativeWind/Tailwind CSS
- **Dark Mode Support**: Automatic dark/light theme switching
- **Responsive Design**: Works on both phones and tablets
- **Real-time Data**: Direct integration with Supabase backend
- **Type Safety**: Full TypeScript implementation
- **State Management**: Zustand for efficient state management

## Database Integration

The app integrates with the following Supabase tables:
- `students`: Student profiles and information
- `teachers`: Teacher profiles and information
- `classes`: Class information and schedules
- `attendance`: Attendance records
- `teacher_classes`: Teacher-class assignments
- `student_classes`: Student-class enrollments

## Authentication Flow

1. **Admin Setup**: Admin creates student/teacher accounts in Supabase with temporary passwords
2. **User Login**: Students/teachers select their role and sign in with email/password
3. **Profile Access**: Users access role-specific features based on their authentication
4. **Password Change**: Users can change their temporary password from profile settings

## Screen Structure

### Student Screens
- **Home**: Dashboard with overview and quick actions
- **Profile**: Detailed profile with attendance history and settings

### Teacher Screens
- **Home**: Welcome screen with quick actions
- **Dashboard**: Student management with grade/attendance updates
- **Classes**: Class management and attendance marking (if implemented)

## Setup Instructions

1. **Environment Variables**: Ensure `.env` file has correct Supabase credentials
2. **Dependencies**: All required packages are installed (Supabase, Zustand, Expo SecureStore)
3. **Database**: Ensure Supabase tables are set up with proper RLS policies
4. **Testing**: Create test accounts for students and teachers in Supabase

## Testing the App

### Prerequisites
1. Supabase project with school management tables
2. Test student and teacher accounts created by admin
3. Expo development environment set up

### Test Scenarios

#### Student Login Test
1. Open the app
2. Select "Student" role
3. Enter student email and password
4. Verify access to student dashboard and profile

#### Teacher Login Test
1. Open the app
2. Select "Teacher" role  
3. Enter teacher email and password
4. Verify access to teacher dashboard and student management

#### Password Change Test
1. Login as student or teacher
2. Go to profile section
3. Change password functionality
4. Verify new password works on next login

#### Data Integration Test
1. Verify student profile shows correct information from database
2. Test attendance history display
3. Test teacher's ability to update student grades
4. Test attendance marking functionality

## Security Features

- **Row Level Security**: Supabase RLS policies ensure users only access their authorized data
- **Secure Storage**: Authentication tokens stored securely using Expo SecureStore
- **Input Validation**: All user inputs are validated and sanitized
- **Role-based Access**: Different features available based on user role
- **Session Management**: Automatic session handling with refresh tokens

## Future Enhancements

- Push notifications for attendance and grade updates
- Offline support for basic functionality
- Parent portal integration
- Assignment submission features
- Real-time messaging between teachers and students
- Calendar integration for class schedules

## Troubleshooting

### Common Issues
1. **Login fails**: Check Supabase credentials and user account status
2. **Data not loading**: Verify RLS policies and user permissions
3. **Styling issues**: Ensure NativeWind is properly configured
4. **Navigation issues**: Check authentication state and role-based routing

### Development Tips
- Use Expo development tools for debugging
- Check Supabase logs for database issues
- Test on both iOS and Android devices
- Verify network connectivity for API calls
