'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import GeofenceMap from './GeofenceMap'
import { validateGeofence, formatValidationErrors } from '@/lib/validation'
import {
  MapPin,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Clock,
  AlertCircle,
  CheckCircle,
  Calendar
} from 'lucide-react'

interface GeofenceData {
  id?: string
  name: string
  center_latitude: number
  center_longitude: number
  radius_meters: number
  allowed_check_in_start?: string
  allowed_check_in_end?: string
  allowed_check_out_start?: string
  allowed_check_out_end?: string
  allowed_days?: number[]
  is_active: boolean
}

interface GeofenceManagerProps {
  onSave?: () => void
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sun' },
  { value: 1, label: 'Mon' },
  { value: 2, label: 'Tue' },
  { value: 3, label: 'Wed' },
  { value: 4, label: 'Thu' },
  { value: 5, label: 'Fri' },
  { value: 6, label: 'Sat' }
]

export default function GeofenceManager({ onSave }: GeofenceManagerProps) {
  const [geofences, setGeofences] = useState<GeofenceData[]>([])
  const [selectedGeofence, setSelectedGeofence] = useState<GeofenceData | null>(null)
  const [editingGeofence, setEditingGeofence] = useState<GeofenceData | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [message, setMessage] = useState('')

  const [formData, setFormData] = useState<GeofenceData>({
    name: '',
    center_latitude: 28.6139,
    center_longitude: 77.2090,
    radius_meters: 100,
    allowed_check_in_start: '07:00',
    allowed_check_in_end: '09:00',
    allowed_check_out_start: '15:00',
    allowed_check_out_end: '18:00',
    allowed_days: [1, 2, 3, 4, 5],
    is_active: true
  })

  useEffect(() => {
    fetchGeofences()
  }, [])

  const fetchGeofences = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/geofences')
      const data = await response.json()

      if (data.success) {
        setGeofences(data.data)
      } else {
        setError(data.error || 'Failed to fetch geofences')
      }
    } catch (err) {
      setError('Failed to fetch geofences')
    } finally {
      setLoading(false)
    }
  }

  const handleLocationSelect = (lat: number, lng: number) => {
    setFormData({
      ...formData,
      center_latitude: lat,
      center_longitude: lng
    })
  }

  const handleInputChange = (field: keyof GeofenceData, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    })
  }

  const handleDayToggle = (day: number) => {
    const currentDays = formData.allowed_days || []
    const newDays = currentDays.includes(day)
      ? currentDays.filter(d => d !== day)
      : [...currentDays, day].sort()
    
    setFormData({
      ...formData,
      allowed_days: newDays
    })
  }

  const startCreating = () => {
    setIsCreating(true)
    setEditingGeofence(null)
    setSelectedGeofence(null)
    setFormData({
      name: '',
      center_latitude: 28.6139,
      center_longitude: 77.2090,
      radius_meters: 100,
      allowed_check_in_start: '07:00',
      allowed_check_in_end: '09:00',
      allowed_check_out_start: '15:00',
      allowed_check_out_end: '18:00',
      allowed_days: [1, 2, 3, 4, 5],
      is_active: true
    })
  }

  const startEditing = (geofence: GeofenceData) => {
    setEditingGeofence(geofence)
    setIsCreating(false)
    setSelectedGeofence(geofence)
    setFormData({
      ...geofence,
      allowed_check_in_start: geofence.allowed_check_in_start?.substring(0, 5) || '07:00',
      allowed_check_in_end: geofence.allowed_check_in_end?.substring(0, 5) || '09:00',
      allowed_check_out_start: geofence.allowed_check_out_start?.substring(0, 5) || '15:00',
      allowed_check_out_end: geofence.allowed_check_out_end?.substring(0, 5) || '18:00'
    })
  }

  const cancelEditing = () => {
    setIsCreating(false)
    setEditingGeofence(null)
    setSelectedGeofence(null)
    setError('')
    setMessage('')
  }

  const saveGeofence = async () => {
    try {
      setLoading(true)
      setError('')

      // Validate the form data
      const validation = validateGeofence({
        name: formData.name,
        center_latitude: formData.center_latitude,
        center_longitude: formData.center_longitude,
        radius_meters: formData.radius_meters,
        allowed_check_in_start: formData.allowed_check_in_start,
        allowed_check_in_end: formData.allowed_check_in_end,
        allowed_check_out_start: formData.allowed_check_out_start,
        allowed_check_out_end: formData.allowed_check_out_end,
        allowed_days: formData.allowed_days
      })

      if (!validation.isValid) {
        setError(formatValidationErrors(validation.errors))
        return
      }

      const payload = {
        ...formData,
        allowed_check_in_start: formData.allowed_check_in_start + ':00',
        allowed_check_in_end: formData.allowed_check_in_end + ':00',
        allowed_check_out_start: formData.allowed_check_out_start + ':00',
        allowed_check_out_end: formData.allowed_check_out_end + ':00'
      }

      const url = editingGeofence ? `/api/geofences/${editingGeofence.id}` : '/api/geofences'
      const method = editingGeofence ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (data.success) {
        setMessage(data.message)
        await fetchGeofences()
        cancelEditing()
        if (onSave) onSave()
        setTimeout(() => setMessage(''), 3000)
      } else {
        setError(data.error || 'Failed to save geofence')
      }
    } catch (err) {
      setError('Failed to save geofence')
    } finally {
      setLoading(false)
    }
  }

  const deleteGeofence = async (geofence: GeofenceData) => {
    if (!confirm(`Are you sure you want to delete "${geofence.name}"?`)) {
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/geofences/${geofence.id}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        setMessage('Geofence deleted successfully')
        await fetchGeofences()
        if (selectedGeofence?.id === geofence.id) {
          setSelectedGeofence(null)
        }
        if (onSave) onSave()
        setTimeout(() => setMessage(''), 3000)
      } else {
        setError(data.error || 'Failed to delete geofence')
      }
    } catch (err) {
      setError('Failed to delete geofence')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Messages */}
      {message && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{message}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Geofence List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  School Locations
                </CardTitle>
                <CardDescription>
                  Manage geofenced areas where teachers can mark attendance
                </CardDescription>
              </div>
              <Button onClick={startCreating} size="sm" disabled={loading}>
                <Plus className="h-4 w-4 mr-2" />
                Add Location
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {loading && geofences.length === 0 ? (
              <div className="text-center py-8 text-gray-500">Loading...</div>
            ) : geofences.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No locations configured. Add your first geofence to get started.
              </div>
            ) : (
              <div className="space-y-3">
                {geofences.map((geofence) => (
                  <div
                    key={geofence.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedGeofence?.id === geofence.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedGeofence(geofence)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{geofence.name}</h4>
                          <Badge variant={geofence.is_active ? 'default' : 'secondary'}>
                            {geofence.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          Radius: {geofence.radius_meters}m
                        </p>
                        {geofence.allowed_check_in_start && (
                          <p className="text-xs text-gray-500 mt-1">
                            Check-in: {geofence.allowed_check_in_start?.substring(0, 5)} - {geofence.allowed_check_in_end?.substring(0, 5)}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            startEditing(geofence)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteGeofence(geofence)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Map */}
        <Card>
          <CardHeader>
            <CardTitle>Location Map</CardTitle>
            <CardDescription>
              {isCreating || editingGeofence
                ? 'Click on the map to set the geofence location'
                : 'View and select existing geofences'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <GeofenceMap
              geofences={geofences}
              selectedGeofence={isCreating || editingGeofence ? formData : selectedGeofence}
              onLocationSelect={handleLocationSelect}
              onGeofenceSelect={setSelectedGeofence}
              editMode={isCreating || !!editingGeofence}
              height="350px"
            />
          </CardContent>
        </Card>
      </div>

      {/* Edit/Create Form */}
      {(isCreating || editingGeofence) && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                {isCreating ? 'Add New Location' : `Edit ${editingGeofence?.name}`}
              </CardTitle>
              <Button variant="outline" onClick={cancelEditing}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="name">Location Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="e.g., Main Campus, Building A"
                />
              </div>
              <div>
                <Label htmlFor="radius">Radius (meters) *</Label>
                <Input
                  id="radius"
                  type="number"
                  min="10"
                  max="1000"
                  value={formData.radius_meters}
                  onChange={(e) => handleInputChange('radius_meters', parseInt(e.target.value))}
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="lat">Latitude</Label>
                <Input
                  id="lat"
                  type="number"
                  step="0.000001"
                  value={formData.center_latitude}
                  onChange={(e) => handleInputChange('center_latitude', parseFloat(e.target.value))}
                />
              </div>
              <div>
                <Label htmlFor="lng">Longitude</Label>
                <Input
                  id="lng"
                  type="number"
                  step="0.000001"
                  value={formData.center_longitude}
                  onChange={(e) => handleInputChange('center_longitude', parseFloat(e.target.value))}
                />
              </div>
            </div>

            <div>
              <Label className="flex items-center gap-2 mb-3">
                <Clock className="h-4 w-4" />
                Check-in Time Window
              </Label>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="checkin-start" className="text-sm">Start Time</Label>
                  <Input
                    id="checkin-start"
                    type="time"
                    value={formData.allowed_check_in_start}
                    onChange={(e) => handleInputChange('allowed_check_in_start', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="checkin-end" className="text-sm">End Time</Label>
                  <Input
                    id="checkin-end"
                    type="time"
                    value={formData.allowed_check_in_end}
                    onChange={(e) => handleInputChange('allowed_check_in_end', e.target.value)}
                  />
                </div>
              </div>
            </div>

            <div>
              <Label className="flex items-center gap-2 mb-3">
                <Clock className="h-4 w-4" />
                Check-out Time Window
              </Label>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="checkout-start" className="text-sm">Start Time</Label>
                  <Input
                    id="checkout-start"
                    type="time"
                    value={formData.allowed_check_out_start}
                    onChange={(e) => handleInputChange('allowed_check_out_start', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="checkout-end" className="text-sm">End Time</Label>
                  <Input
                    id="checkout-end"
                    type="time"
                    value={formData.allowed_check_out_end}
                    onChange={(e) => handleInputChange('allowed_check_out_end', e.target.value)}
                  />
                </div>
              </div>
            </div>

            <div>
              <Label className="flex items-center gap-2 mb-3">
                <Calendar className="h-4 w-4" />
                Allowed Days
              </Label>
              <div className="flex gap-2 flex-wrap">
                {DAYS_OF_WEEK.map((day) => (
                  <Button
                    key={day.value}
                    type="button"
                    variant={formData.allowed_days?.includes(day.value) ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleDayToggle(day.value)}
                  >
                    {day.label}
                  </Button>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                />
                <Label htmlFor="active">Active</Label>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" onClick={cancelEditing}>
                  Cancel
                </Button>
                <Button onClick={saveGeofence} disabled={loading}>
                  {loading ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {isCreating ? 'Create Location' : 'Update Location'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
