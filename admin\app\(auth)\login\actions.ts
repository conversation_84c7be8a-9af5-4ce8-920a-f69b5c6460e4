'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'

export async function login(formData: FormData) {
  const supabase = await createClient()

  // Extract and validate form data
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  // Basic validation
  if (!email || !password) {
    redirect('/error?message=' + encodeURIComponent('Email and password are required'))
  }

  if (!email.includes('@')) {
    redirect('/error?message=' + encodeURIComponent('Please enter a valid email address'))
  }

  console.log('Attempting login for email:', email)

  const { data, error } = await supabase.auth.signInWithPassword({
    email: email.trim(),
    password,
  })

  console.log('Login response:', {
    hasData: !!data,
    hasUser: !!data?.user,
    hasSession: !!data?.session,
    error: error?.message
  })

  if (error) {
    console.error('Login error:', error)
    let errorMessage = 'Login failed. Please try again.'

    if (error.message.includes('Invalid login credentials')) {
      errorMessage = 'Invalid email or password. Please check your credentials and try again.'
    } else if (error.message.includes('Email not confirmed')) {
      errorMessage = 'Please check your email and click the confirmation link before signing in.'
    } else if (error.message.includes('Too many requests')) {
      errorMessage = 'Too many login attempts. Please wait a moment and try again.'
    } else {
      errorMessage = `Login failed: ${error.message}`
    }

    redirect('/error?message=' + encodeURIComponent(errorMessage))
  }

  if (!data?.user) {
    console.error('No user data received after successful auth')
    redirect('/error?message=' + encodeURIComponent('Login failed. No user data received.'))
  }

  console.log('Login successful for user:', data.user.email, 'redirecting to dashboard')

  // Revalidate all relevant paths to ensure fresh data
  revalidatePath('/', 'layout')
  revalidatePath('/dashboard', 'page')
  revalidatePath('/dashboard')

  redirect('/dashboard')
}

export async function signup(formData: FormData) {
  const supabase = await createClient()

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  const { error } = await supabase.auth.signUp(data)

  if (error) {
    redirect('/error')
  }

  revalidatePath('/', 'layout')
  redirect('/dashboard')
}
