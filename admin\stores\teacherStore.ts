import { create } from 'zustand'
import { createClient } from '@/utils/supabase/client'
import { Teacher, Class } from '@/lib/supabase'

interface TeacherFormData {
  name: string
  email: string
  phone?: string
  subject?: string
  department?: string
  teacherId: string
}

export interface TeacherCredentials {
  teacherId: string
  temporaryPassword: string
  message: string
}

interface TeacherWithClasses extends Teacher {
  classes?: Class[]
  classCount?: number
}

interface TeacherStore {
  // State
  teachers: TeacherWithClasses[]
  loading: boolean
  error: string | null

  // Actions
  fetchTeachers: () => Promise<void>
  addTeacher: (teacherData: TeacherFormData) => Promise<TeacherCredentials | null>
  updateTeacher: (id: string, updates: Partial<TeacherFormData>) => Promise<void>
  deleteTeacher: (id: string) => Promise<void>
  assignTeacherToClass: (teacherId: string, classId: string, isPrimary?: boolean) => Promise<void>
  removeTeacherFromClass: (teacherId: string, classId: string) => Promise<void>
  clearError: () => void
}

export const useTeacherStore = create<TeacherStore>((set, get) => ({
  // Initial state
  teachers: [],
  loading: false,
  error: null,

  // Fetch all teachers for the current school
  fetchTeachers: async () => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      // Get user's school
      const { data: adminData } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      if (!adminData) {
        throw new Error('User is not a school admin')
      }

      // Fetch teachers with class assignments
      const { data: teachersData, error: teachersError } = await supabase
        .from('teachers')
        .select(`
          *,
          teacher_classes(
            class:classes(
              id,
              class_name,
              grade,
              section,
              subject,
              academic_year
            ),
            is_primary
          )
        `)
        .eq('school_id', adminData.school_id)
        .eq('is_active', true)
        .order('name', { ascending: true })

      if (teachersError) {
        throw teachersError
      }

      // Transform the data
      const transformedTeachers: TeacherWithClasses[] = (teachersData || []).map(teacher => ({
        ...teacher,
        classes: teacher.teacher_classes?.map((tc: any) => tc.class) || [],
        classCount: teacher.teacher_classes?.length || 0
      }))

      set({ teachers: transformedTeachers, loading: false })
    } catch (error) {
      console.error('Error fetching teachers:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch teachers',
        loading: false 
      })
    }
  },

  // Add a new teacher
  addTeacher: async (teacherData: TeacherFormData): Promise<TeacherCredentials | null> => {
    set({ loading: true, error: null })

    try {
      // Create FormData for server action
      const formData = new FormData()
      formData.append('name', teacherData.name)
      formData.append('email', teacherData.email)
      formData.append('phone', teacherData.phone || '')
      formData.append('subject', teacherData.subject || '')
      formData.append('department', teacherData.department || '')
      formData.append('teacherId', teacherData.teacherId)

      // Import the server action
      const { registerTeacher } = await import('@/app/(admin)/teachers/actions')
      const result = await registerTeacher(formData)

      if (result.error) {
        throw new Error(result.error)
      }

      // Refresh the teachers list
      await get().fetchTeachers()
      set({ loading: false })

      // Return the result from server action
      return {
        teacherId: result.teacherId!,
        temporaryPassword: result.temporaryPassword!,
        message: result.message!
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to add teacher',
        loading: false
      })
      return null
    }
  },

  // Update an existing teacher
  updateTeacher: async (id: string, updates: Partial<TeacherFormData>) => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()

      const updateData: any = {
        updated_at: new Date().toISOString()
      }

      if (updates.name) updateData.name = updates.name
      if (updates.email) updateData.email = updates.email
      if (updates.phone !== undefined) updateData.phone = updates.phone
      if (updates.subject !== undefined) updateData.subject = updates.subject
      if (updates.department !== undefined) updateData.department = updates.department
      if (updates.teacherId) updateData.teacher_id = updates.teacherId

      // Check for duplicate teacher ID if it's being updated
      if (updates.teacherId) {
        const { data: existingTeacher } = await supabase
          .from('teachers')
          .select('id')
          .eq('teacher_id', updates.teacherId)
          .neq('id', id)
          .single()

        if (existingTeacher) {
          throw new Error('Teacher ID already exists')
        }
      }

      // Check for duplicate email if it's being updated
      if (updates.email) {
        const { data: existingEmail } = await supabase
          .from('teachers')
          .select('id')
          .eq('email', updates.email)
          .neq('id', id)
          .single()

        if (existingEmail) {
          throw new Error('Email already exists')
        }
      }

      const { error: updateError } = await supabase
        .from('teachers')
        .update(updateData)
        .eq('id', id)

      if (updateError) {
        throw updateError
      }

      // Refresh teachers list
      await get().fetchTeachers()
      set({ loading: false })
    } catch (error) {
      console.error('Error updating teacher:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update teacher',
        loading: false 
      })
    }
  },

  // Delete a teacher (hard delete with all related data)
  deleteTeacher: async (id: string) => {
    set({ loading: true, error: null })

    try {
      // Import the server action
      const { deleteTeacher: deleteTeacherAction } = await import('@/app/(admin)/teachers/actions')
      const result = await deleteTeacherAction(id)

      if (result.error) {
        throw new Error(result.error)
      }

      // Refresh the teachers list
      await get().fetchTeachers()
      set({ loading: false })
    } catch (error) {
      console.error('Error deleting teacher:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to delete teacher',
        loading: false
      })
    }
  },

  // Assign teacher to class
  assignTeacherToClass: async (teacherId: string, classId: string, isPrimary: boolean = false) => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()
      
      // Get current user's school
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      const { data: adminData } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      if (!adminData) {
        throw new Error('User is not a school admin')
      }

      // Check if assignment already exists
      const { data: existingAssignment } = await supabase
        .from('teacher_classes')
        .select('id')
        .eq('teacher_id', teacherId)
        .eq('class_id', classId)
        .single()

      if (existingAssignment) {
        throw new Error('Teacher is already assigned to this class')
      }

      // Insert the assignment
      const { error: insertError } = await supabase
        .from('teacher_classes')
        .insert([{
          teacher_id: teacherId,
          class_id: classId,
          school_id: adminData.school_id,
          is_primary: isPrimary
        }])

      if (insertError) {
        throw insertError
      }

      // Refresh teachers list
      await get().fetchTeachers()
      set({ loading: false })
    } catch (error) {
      console.error('Error assigning teacher to class:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to assign teacher to class',
        loading: false 
      })
    }
  },

  // Remove teacher from class
  removeTeacherFromClass: async (teacherId: string, classId: string) => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()

      const { error: deleteError } = await supabase
        .from('teacher_classes')
        .delete()
        .eq('teacher_id', teacherId)
        .eq('class_id', classId)

      if (deleteError) {
        throw deleteError
      }

      // Refresh teachers list
      await get().fetchTeachers()
      set({ loading: false })
    } catch (error) {
      console.error('Error removing teacher from class:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to remove teacher from class',
        loading: false 
      })
    }
  },

  // Clear error
  clearError: () => {
    set({ error: null })
  }
}))
