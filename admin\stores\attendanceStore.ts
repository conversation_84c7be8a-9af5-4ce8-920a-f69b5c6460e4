import { create } from 'zustand'
import { createClient } from '@/utils/supabase/client'
import {
  Attendance,
  AttendanceWithDetails,
  AttendanceStatus,
  Class,
  Teacher,
  Student
} from '@/lib/supabase'
import { markAttendance as markAttendanceAction, updateAttendanceRecord as updateAttendanceAction } from '@/app/(admin)/attendance/actions'

interface AttendanceStats {
  totalStudents: number
  presentCount: number
  absentCount: number
  lateCount: number
  excusedCount: number
  attendanceRate: number
}

interface ClassWithStudents extends Class {
  students: Student[]
  teacher?: Teacher
}

interface StudentWithClass extends Student {
  class_name?: string
  section?: string
  class_id?: string
}

interface AttendanceStore {
  // State
  attendance: AttendanceWithDetails[]
  classes: ClassWithStudents[]
  currentClass: ClassWithStudents | null
  attendanceStats: AttendanceStats | null
  loading: boolean
  error: string | null
  realtimeEnabled: boolean

  // New grade-based state
  grades: string[]
  selectedGrade: string | null
  studentsInGrade: StudentWithClass[]
  markingMode: 'bulk' | 'individual'
  attendanceByGrade: Record<string, {
    id?: string
    status: AttendanceStatus
    notes: string
    classId: string
  }>

  // Actions
  fetchClasses: () => Promise<void>
  fetchAttendanceForClass: (classId: string, date?: string) => Promise<void>
  fetchAttendanceStats: (classId?: string, startDate?: string, endDate?: string) => Promise<void>
  markAttendance: (studentId: string, classId: string, status: AttendanceStatus, date?: string, notes?: string) => Promise<void>
  bulkMarkAttendance: (attendanceRecords: Array<{
    studentId: string
    classId: string
    status: AttendanceStatus
    notes?: string
  }>, date?: string) => Promise<void>
  updateAttendance: (attendanceId: string, status: AttendanceStatus, notes?: string) => Promise<void>
  setCurrentClass: (classId: string) => void
  clearError: () => void
  setRealtimeEnabled: (enabled: boolean) => void

  // New grade-based actions
  fetchGrades: () => Promise<void>
  fetchStudentsByGrade: (grade: string) => Promise<void>
  fetchAttendanceByGrade: (grade: string, date: string) => Promise<void>
  setSelectedGrade: (grade: string | null) => void
  setMarkingMode: (mode: 'bulk' | 'individual') => void
  bulkMarkAttendanceByGrade: (attendanceRecords: Array<{
    studentId: string
    classId: string
    status: AttendanceStatus
    notes?: string
  }>, date?: string) => Promise<void>

  // Real-time subscription
  subscribeToAttendance: (classId: string) => () => void
}

export const useAttendanceStore = create<AttendanceStore>((set, get) => ({
  // Initial state
  attendance: [],
  classes: [],
  currentClass: null,
  attendanceStats: null,
  loading: false,
  error: null,
  realtimeEnabled: true,

  // New grade-based state
  grades: [],
  selectedGrade: null,
  studentsInGrade: [],
  markingMode: 'individual',
  attendanceByGrade: {},

  // Fetch classes assigned to current user (teacher) or all classes (admin)
  fetchClasses: async () => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      // Check if user is admin or teacher
      const { data: adminData } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      const { data: teacherData } = await supabase
        .from('teachers')
        .select('id, school_id')
        .eq('user_id', user.id)
        .single()

      let classesQuery

      // If user is teacher, only get their assigned classes
      if (teacherData && !adminData) {
        classesQuery = supabase
          .from('classes')
          .select(`
            *,
            student_classes!inner(
              student:students(
                id,
                name,
                student_id,
                email,
                grade
              )
            ),
            teacher_classes!inner(
              teacher:teachers(
                id,
                name,
                teacher_id,
                email,
                subject
              )
            )
          `)
          .eq('teacher_classes.teacher_id', teacherData.id)
      } else if (adminData) {
        // If admin, get ALL classes in their school (with or without students)
        classesQuery = supabase
          .from('classes')
          .select(`
            *,
            student_classes(
              student:students(
                id,
                name,
                student_id,
                email,
                grade
              )
            ),
            teacher_classes(
              teacher:teachers(
                id,
                name,
                teacher_id,
                email,
                subject
              )
            )
          `)
          .eq('school_id', adminData.school_id)
          .eq('is_active', true)
      } else {
        throw new Error('User is neither admin nor teacher')
      }

      const { data: classesData, error: classesError } = await classesQuery

      if (classesError) {
        throw classesError
      }

      // Transform the data to match our interface
      const transformedClasses: ClassWithStudents[] = (classesData || []).map(classItem => ({
        ...classItem,
        students: classItem.student_classes?.map((sc: any) => sc.student) || [],
        teacher: classItem.teacher_classes?.[0]?.teacher
      }))

      set({ classes: transformedClasses, loading: false })
    } catch (error) {
      console.error('Error fetching classes:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch classes',
        loading: false 
      })
    }
  },

  // Fetch attendance records for a specific class and date
  fetchAttendanceForClass: async (classId: string, date?: string) => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()
      const attendanceDate = date || new Date().toISOString().split('T')[0]

      const { data, error } = await supabase
        .from('attendance')
        .select(`
          *,
          student:students(
            id,
            name,
            student_id
          ),
          class:classes(
            id,
            class_name,
            grade,
            section
          )
        `)
        .eq('class_id', classId)
        .eq('attendance_date', attendanceDate)
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }

      const transformedData: AttendanceWithDetails[] = (data || []).map(record => ({
        ...record,
        student: record.student,
        class: record.class,
        marked_by_user: { id: record.marked_by, name: 'Unknown' } // Will be populated separately if needed
      }))

      set({ attendance: transformedData, loading: false })
    } catch (error) {
      console.error('Error fetching attendance:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch attendance',
        loading: false 
      })
    }
  },

  // Fetch attendance statistics
  fetchAttendanceStats: async (classId?: string, startDate?: string, endDate?: string) => {
    try {
      const supabase = createClient()
      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      const end = endDate || new Date().toISOString().split('T')[0]

      let query = supabase
        .from('attendance')
        .select('status, student_id')
        .gte('attendance_date', start)
        .lte('attendance_date', end)

      if (classId) {
        query = query.eq('class_id', classId)
      }

      const { data, error } = await query

      if (error) {
        throw error
      }

      // Calculate statistics
      const totalRecords = data?.length || 0
      const uniqueStudents = new Set(data?.map(record => record.student_id)).size
      const presentCount = data?.filter(record => record.status === 'present').length || 0
      const absentCount = data?.filter(record => record.status === 'absent').length || 0
      const lateCount = data?.filter(record => record.status === 'late').length || 0
      const excusedCount = data?.filter(record => record.status === 'excused').length || 0

      const stats: AttendanceStats = {
        totalStudents: uniqueStudents,
        presentCount,
        absentCount,
        lateCount,
        excusedCount,
        attendanceRate: totalRecords > 0 ? (presentCount / totalRecords) * 100 : 0
      }

      set({ attendanceStats: stats })
    } catch (error) {
      console.error('Error fetching attendance stats:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch attendance statistics'
      })
    }
  },

  // Mark attendance for a single student
  markAttendance: async (studentId: string, classId: string, status: AttendanceStatus, date?: string, notes?: string) => {
    try {
      const result = await markAttendanceAction([{
        studentId,
        classId,
        status,
        notes
      }], date)

      if (result.error) {
        throw new Error(result.error)
      }

      // Refresh attendance data
      const attendanceDate = date || new Date().toISOString().split('T')[0]
      await get().fetchAttendanceForClass(classId, attendanceDate)
    } catch (error) {
      console.error('Error marking attendance:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to mark attendance'
      })
    }
  },

  // Bulk mark attendance for multiple students
  bulkMarkAttendance: async (attendanceRecords, date?: string) => {
    try {
      const result = await markAttendanceAction(attendanceRecords, date)

      if (result.error) {
        throw new Error(result.error)
      }

      // Refresh attendance data
      if (attendanceRecords.length > 0) {
        const attendanceDate = date || new Date().toISOString().split('T')[0]
        await get().fetchAttendanceForClass(attendanceRecords[0].classId, attendanceDate)
      }
    } catch (error) {
      console.error('Error bulk marking attendance:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to bulk mark attendance'
      })
    }
  },

  // Update existing attendance record
  updateAttendance: async (attendanceId: string, status: AttendanceStatus, notes?: string) => {
    try {
      const result = await updateAttendanceAction(attendanceId, status, notes)

      if (result.error) {
        throw new Error(result.error)
      }

      // Update local state
      set(state => ({
        attendance: state.attendance.map(record =>
          record.id === attendanceId
            ? { ...record, status, notes }
            : record
        )
      }))
    } catch (error) {
      console.error('Error updating attendance:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to update attendance'
      })
    }
  },

  // Set current class
  setCurrentClass: (classId: string) => {
    const classes = get().classes
    const currentClass = classes.find(c => c.id === classId) || null
    set({ currentClass })
  },

  // Clear error
  clearError: () => {
    set({ error: null })
  },

  // Set real-time enabled
  setRealtimeEnabled: (enabled: boolean) => {
    set({ realtimeEnabled: enabled })
  },

  // Subscribe to real-time attendance updates
  subscribeToAttendance: (classId: string) => {
    const supabase = createClient()

    const subscription = supabase
      .channel(`attendance-${classId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'attendance',
          filter: `class_id=eq.${classId}`
        },
        (payload) => {
          console.log('Real-time attendance update received:', payload)

          // Handle different types of changes
          if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
            // Refresh attendance data for the current date
            const currentDate = new Date().toISOString().split('T')[0]
            get().fetchAttendanceForClass(classId, currentDate)

            // Also refresh stats
            get().fetchAttendanceStats(classId)
          } else if (payload.eventType === 'DELETE') {
            // Remove the deleted record from local state
            set(state => ({
              attendance: state.attendance.filter(record => record.id !== payload.old.id)
            }))
          }
        }
      )
      .subscribe((status) => {
        console.log('Attendance subscription status:', status)
      })

    // Return unsubscribe function
    return () => {
      console.log('Unsubscribing from attendance updates for class:', classId)
      subscription.unsubscribe()
    }
  },

  // New grade-based functions
  fetchGrades: async () => {
    set({ loading: true, error: null })

    try {
      const supabase = createClient()

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      // Get admin's school
      const { data: adminData, error: adminError } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      if (adminError || !adminData) {
        throw new Error('Admin school not found')
      }

      // Fetch distinct grades from classes table (more reliable than students table)
      const { data: gradesData, error: gradesError } = await supabase
        .from('classes')
        .select('grade')
        .eq('school_id', adminData.school_id)
        .eq('is_active', true)
        .order('grade')

      if (gradesError) {
        throw gradesError
      }

      // Extract unique grades
      const uniqueGrades = [...new Set(gradesData?.map(item => item.grade) || [])]
        .filter(grade => grade !== null && grade !== undefined)
        .sort((a, b) => parseInt(a) - parseInt(b))

      set({ grades: uniqueGrades, loading: false })
    } catch (error) {
      console.error('Error fetching grades:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch grades',
        loading: false
      })
    }
  },

  fetchStudentsByGrade: async (grade: string) => {
    set({ loading: true, error: null })

    try {
      const supabase = createClient()

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      // Get admin's school
      const { data: adminData, error: adminError } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      if (adminError || !adminData) {
        throw new Error('Admin school not found')
      }

      // First, get all classes of the selected grade
      const { data: classesData, error: classesError } = await supabase
        .from('classes')
        .select('id, class_name, section, grade')
        .eq('school_id', adminData.school_id)
        .eq('is_active', true)
        .eq('grade', grade)

      if (classesError) {
        throw classesError
      }

      if (!classesData || classesData.length === 0) {
        // No classes found for this grade
        set({ studentsInGrade: [], loading: false })
        return
      }

      // Get class IDs
      const classIds = classesData.map(cls => cls.id)

      // Now fetch students enrolled in these classes
      const { data: studentsData, error: studentsError } = await supabase
        .from('student_classes')
        .select(`
          student:students(
            id,
            name,
            student_id,
            email,
            grade
          ),
          class_id
        `)
        .eq('school_id', adminData.school_id)
        .eq('is_active', true)
        .in('class_id', classIds)

      if (studentsError) {
        throw studentsError
      }

      if (!studentsData || studentsData.length === 0) {
        // No students enrolled in classes of this grade
        set({ studentsInGrade: [], loading: false })
        return
      }

      // Transform the data to include class information
      const transformedStudents: StudentWithClass[] = (studentsData || []).map(enrollment => {
        const student = enrollment.student
        const classInfo = classesData.find(cls => cls.id === enrollment.class_id)
        return {
          ...student,
          class_name: classInfo?.class_name,
          section: classInfo?.section,
          class_id: classInfo?.id
        }
      })

      set({ studentsInGrade: transformedStudents, loading: false })
    } catch (error) {
      console.error('Error fetching students by grade:', error)
      const errorMessage = error instanceof Error
        ? error.message
        : 'Failed to fetch students for the selected grade'
      set({
        error: errorMessage,
        studentsInGrade: [],
        loading: false
      })
    }
  },

  setSelectedGrade: (grade: string | null) => {
    set({ selectedGrade: grade, studentsInGrade: [] })
    if (grade) {
      get().fetchStudentsByGrade(grade)
    }
  },

  setMarkingMode: (mode: 'bulk' | 'individual') => {
    set({ markingMode: mode })
  },

  fetchAttendanceByGrade: async (grade: string, date: string) => {
    set({ loading: true, error: null })

    try {
      const supabase = createClient()

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      // Get user's school
      const { data: adminData } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      if (!adminData) {
        throw new Error('User is not a school admin')
      }

      // Get all classes of the selected grade
      const { data: classesData, error: classesError } = await supabase
        .from('classes')
        .select('id')
        .eq('school_id', adminData.school_id)
        .eq('is_active', true)
        .eq('grade', grade)

      if (classesError) {
        throw classesError
      }

      if (!classesData || classesData.length === 0) {
        set({ attendanceByGrade: {}, loading: false })
        return
      }

      const classIds = classesData.map(cls => cls.id)

      // Fetch attendance records for the date and classes
      const { data: attendanceData, error: attendanceError } = await supabase
        .from('attendance')
        .select(`
          id,
          student_id,
          class_id,
          status,
          notes,
          student:students(
            id,
            name,
            student_id,
            grade
          )
        `)
        .eq('school_id', adminData.school_id)
        .eq('attendance_date', date)
        .in('class_id', classIds)

      if (attendanceError) {
        throw attendanceError
      }

      // Transform attendance data into a lookup object
      const attendanceByStudent: Record<string, any> = {}
      if (attendanceData) {
        attendanceData.forEach(record => {
          attendanceByStudent[record.student_id] = {
            id: record.id,
            status: record.status,
            notes: record.notes,
            classId: record.class_id
          }
        })
      }

      set({ attendanceByGrade: attendanceByStudent, loading: false })
    } catch (error) {
      console.error('Error fetching attendance by grade:', error)
      const errorMessage = error instanceof Error
        ? error.message
        : 'Failed to fetch attendance records'
      set({
        error: errorMessage,
        attendanceByGrade: {},
        loading: false
      })
    }
  },

  bulkMarkAttendanceByGrade: async (attendanceRecords, date) => {
    set({ loading: true, error: null })

    try {
      // Import the server action
      const { markAttendance: markAttendanceAction } = await import('@/app/(admin)/attendance/actions')
      const result = await markAttendanceAction(attendanceRecords, date)

      if (result.error) {
        throw new Error(result.error)
      }

      // Refresh attendance data if we have a current class
      const { currentClass } = get()
      if (currentClass) {
        await get().fetchAttendanceForClass(currentClass.id, date)
      }

      set({ loading: false })
    } catch (error) {
      console.error('Error marking bulk attendance:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to mark attendance',
        loading: false
      })
    }
  }
}))
