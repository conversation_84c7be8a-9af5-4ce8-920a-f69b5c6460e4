import { Student } from '@/lib/supabase';
import { useAuthStore } from '@/stores/authStore';
import { useStudentStore } from '@/stores/studentStore';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, FlatList, Modal, RefreshControl, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function TeacherDashboard() {
  const router = useRouter();
  const { user, signOut } = useAuthStore();
  const { students, fetchStudents, fetchStudentsPaginated, updateStudent, isLoading, error } = useStudentStore();
  const [showGradeModal, setShowGradeModal] = useState(false);
  const [showAttendanceModal, setShowAttendanceModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [newGrade, setNewGrade] = useState('');
  const [availableGrades, setAvailableGrades] = useState<string[]>([]);

  // Pagination and caching state
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreStudents, setHasMoreStudents] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [cachedStudents, setCachedStudents] = useState<Student[]>([]);
  const [lastCacheTime, setLastCacheTime] = useState<number>(0);

  const STUDENTS_PER_PAGE = 5;
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  const [attendanceStatus, setAttendanceStatus] = useState<'present' | 'absent' | 'late' | 'excused'>('present');
  const [attendanceNotes, setAttendanceNotes] = useState('');

  // Check if cache is valid
  const isCacheValid = () => {
    const now = Date.now();
    return (now - lastCacheTime) < CACHE_DURATION && cachedStudents.length > 0;
  };

  // Load initial students with caching
  const loadInitialStudents = async (forceRefresh = false) => {
    if (!user?.profile?.school_id) {
      console.warn('No school_id found in user profile:', user?.profile);
      return;
    }

    // Use cache if valid and not forcing refresh
    if (!forceRefresh && isCacheValid()) {
      console.log('Using cached students');
      return;
    }

    try {
      console.log('Loading initial students for school:', user.profile.school_id);
      const result = await fetchStudentsPaginated(user.profile.school_id, 1, STUDENTS_PER_PAGE);

      setCachedStudents(result.students);
      setHasMoreStudents(result.hasMore);
      setCurrentPage(1);
      setLastCacheTime(Date.now());

      console.log(`Loaded ${result.students.length} students, hasMore: ${result.hasMore}`);
    } catch (error) {
      console.error('Error loading initial students:', error);
    }
  };

  // Load more students for pagination
  const loadMoreStudents = async () => {
    if (!user?.profile?.school_id || isLoadingMore || !hasMoreStudents) {
      return;
    }

    setIsLoadingMore(true);
    try {
      const nextPage = currentPage + 1;
      console.log(`Loading page ${nextPage} of students`);

      const result = await fetchStudentsPaginated(user.profile.school_id, nextPage, STUDENTS_PER_PAGE);

      setCachedStudents(prev => [...prev, ...result.students]);
      setHasMoreStudents(result.hasMore);
      setCurrentPage(nextPage);

      console.log(`Loaded ${result.students.length} more students, hasMore: ${result.hasMore}`);
    } catch (error) {
      console.error('Error loading more students:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Pull to refresh
  const onRefresh = async () => {
    setIsRefreshing(true);
    try {
      await loadInitialStudents(true);
      await fetchAvailableGrades();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    if (user) {
      loadInitialStudents();
      fetchAvailableGrades();
    }
  }, [user]);

  const fetchAvailableGrades = async () => {
    try {
      const { supabase } = await import('@/lib/supabase');

      const { data, error } = await supabase
        .from('classes')
        .select('grade_level')
        .eq('school_id', user?.profile?.school_id)
        .not('grade_level', 'is', null);

      if (error) {
        console.error('Error fetching grades:', error);
        setAvailableGrades(['9th Grade', '10th Grade', '11th Grade', '12th Grade']);
        return;
      }

      const uniqueGrades = [...new Set(data.map(item => item.grade_level).filter(Boolean))];

      if (uniqueGrades.length === 0) {
        setAvailableGrades(['9th Grade', '10th Grade', '11th Grade', '12th Grade']);
      } else {
        setAvailableGrades(uniqueGrades);
      }
    } catch (error) {
      console.error('Error fetching available grades:', error);
      setAvailableGrades(['9th Grade', '10th Grade', '11th Grade', '12th Grade']);
    }
  };

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await signOut();
            router.replace('/(auth)/sign-in');
          }
        },
      ]
    );
  };

  const handleUpdateGrade = async () => {
    if (!selectedStudent || !newGrade.trim()) {
      Alert.alert('Error', 'Please enter a valid grade');
      return;
    }

    try {
      await updateStudent(selectedStudent.id, { grade: newGrade.trim() });
      Alert.alert('Success', 'Grade updated successfully');
      setShowGradeModal(false);
      setSelectedStudent(null);
      setNewGrade('');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleMarkAttendance = async () => {
    Alert.alert('Coming Soon', 'Student attendance marking will be available in the next update.');
    setShowAttendanceModal(false);
  };

  const openGradeModal = (student: Student) => {
    setSelectedStudent(student);
    setNewGrade(student.grade || '');
    setShowGradeModal(true);
  };

  const openAttendanceModal = (student: Student) => {
    setSelectedStudent(student);
    setAttendanceStatus('present');
    setAttendanceNotes('');
    setShowAttendanceModal(true);
  };

  const renderStudentCard = ({ item: student }: { item: Student }) => (
    <View className="bg-white dark:bg-neutral-800 rounded-xl p-5 mb-5 shadow-sm">
      <View className="flex-row justify-between items-start mb-3">
        <View className="flex-1">
          <Text className="text-lg font-rubik-bold text-neutral-900 dark:text-neutral-100">
            {student.name}
          </Text>
          <Text className="text-sm font-rubik text-neutral-500 dark:text-neutral-400">
            {student.email}
          </Text>
          <Text className="text-sm font-rubik text-neutral-600 dark:text-neutral-300 mt-1">
            Grade: {student.grade || 'Not assigned'}
          </Text>
        </View>
      </View>

      <View className="flex-row gap-4">
        <TouchableOpacity
          onPress={() => openGradeModal(student)}
          className="flex-1 bg-primary-50 dark:bg-primary-900/20 py-4 rounded-lg"
        >
          <Text className="text-primary-700 dark:text-primary-300 font-rubik-medium text-center">
            Update Grade
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => openAttendanceModal(student)}
          className="flex-1 bg-secondary-50 dark:bg-secondary-900/20 py-4 rounded-lg"
        >
          <Text className="text-secondary-700 dark:text-secondary-300 font-rubik-medium text-center">
            Mark Attendance
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View className="flex-1 bg-light-background dark:bg-dark-background">
      {/* Header */}
      <View className="bg-secondary-500 pt-12 pb-6 px-6">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-white text-2xl font-rubik-bold">
              Teacher Dashboard
            </Text>
            <Text className="text-secondary-100 font-rubik">
              Manage Students
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleSignOut}
            className="bg-white/20 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-rubik-medium">Sign Out</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Students List */}
      <View className="flex-1 px-6 py-6">
        <View className="flex-row justify-between items-center mb-4">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100">
            Students ({cachedStudents.length})
          </Text>
          <TouchableOpacity
            onPress={() => router.push('/(teacher)/students')}
            className="bg-secondary-500 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-rubik-medium">View All</Text>
          </TouchableOpacity>
        </View>

        {isLoading && cachedStudents.length === 0 ? (
          <View className="flex-1 justify-center items-center">
            <Text className="text-neutral-500 dark:text-neutral-400 font-rubik">
              Loading students...
            </Text>
          </View>
        ) : cachedStudents.length > 0 ? (
          <FlatList
            data={cachedStudents}
            renderItem={renderStudentCard}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={onRefresh}
                colors={['#6366f1']}
                tintColor="#6366f1"
              />
            }
            onEndReached={loadMoreStudents}
            onEndReachedThreshold={0.1}
            ListFooterComponent={() => {
              if (isLoadingMore) {
                return (
                  <View className="py-4 items-center">
                    <Text className="text-neutral-500 dark:text-neutral-400 font-rubik">
                      Loading more students...
                    </Text>
                  </View>
                );
              }
              if (!hasMoreStudents && cachedStudents.length > STUDENTS_PER_PAGE) {
                return (
                  <View className="py-4 items-center">
                    <Text className="text-neutral-400 dark:text-neutral-500 font-rubik text-sm">
                      All students loaded
                    </Text>
                  </View>
                );
              }
              return null;
            }}
          />
        ) : (
          <View className="flex-1 justify-center items-center">
            <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center">
              No students found
            </Text>
          </View>
        )}
      </View>

      {/* Grade Update Modal */}
      <Modal
        visible={showGradeModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-light-background dark:bg-dark-background">
          <View className="flex-row justify-between items-center p-6 border-b border-neutral-200 dark:border-neutral-700">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100">
              Update Grade
            </Text>
            <TouchableOpacity onPress={() => setShowGradeModal(false)}>
              <Text className="text-primary-500 font-rubik-medium">Cancel</Text>
            </TouchableOpacity>
          </View>

          <View className="p-6">
            <Text className="text-lg font-rubik-semibold text-neutral-900 dark:text-neutral-100 mb-2">
              {selectedStudent?.name}
            </Text>
            <Text className="text-sm font-rubik text-neutral-500 dark:text-neutral-400 mb-6">
              {selectedStudent?.email}
            </Text>

            <View className="mb-6">
              <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-4">
                Select Grade Level
              </Text>

              <View className="space-y-4">
                {availableGrades.map((grade, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => setNewGrade(grade)}
                    className={`p-5 rounded-lg border-2 ${
                      newGrade === grade
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800'
                    }`}
                  >
                    <View className="flex-row justify-between items-center">
                      <Text className={`font-rubik-semibold ${
                        newGrade === grade
                          ? 'text-primary-700 dark:text-primary-300'
                          : 'text-neutral-900 dark:text-neutral-100'
                      }`}>
                        {grade}
                      </Text>
                      {newGrade === grade && (
                        <View className="bg-primary-500 rounded-full w-6 h-6 items-center justify-center">
                          <Text className="text-white text-xs font-rubik-bold">✓</Text>
                        </View>
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>

              {availableGrades.length === 0 && (
                <View className="py-4">
                  <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center">
                    Loading grade levels...
                  </Text>
                </View>
              )}
            </View>

            <TouchableOpacity
              onPress={handleUpdateGrade}
              disabled={!newGrade}
              className={`w-full py-4 rounded-xl ${
                newGrade ? 'bg-primary-500' : 'bg-gray-300 dark:bg-gray-600'
              }`}
            >
              <Text className={`text-center font-rubik-semibold text-lg ${
                newGrade ? 'text-white' : 'text-gray-500 dark:text-gray-400'
              }`}>
                Update Grade
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Attendance Modal */}
      <Modal
        visible={showAttendanceModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-light-background dark:bg-dark-background">
          <View className="flex-row justify-between items-center p-6 border-b border-neutral-200 dark:border-neutral-700">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100">
              Mark Attendance
            </Text>
            <TouchableOpacity onPress={() => setShowAttendanceModal(false)}>
              <Text className="text-secondary-500 font-rubik-medium">Cancel</Text>
            </TouchableOpacity>
          </View>

          <View className="p-6">
            <Text className="text-lg font-rubik-semibold text-neutral-900 dark:text-neutral-100 mb-2">
              {selectedStudent?.name}
            </Text>
            <Text className="text-sm font-rubik text-neutral-500 dark:text-neutral-400 mb-6">
              {new Date().toLocaleDateString()}
            </Text>

            <View className="mb-6">
              <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-3">
                Attendance Status
              </Text>
              <View className="flex flex-col gap-3">
                {(['present', 'absent', 'late', 'excused'] as const).map((status) => (
                  <TouchableOpacity
                    key={status}
                    onPress={() => setAttendanceStatus(status)}
                    className={`p-4 rounded-xl border-2 ${
                      attendanceStatus === status
                        ? 'border-secondary-500 bg-secondary-50 dark:bg-secondary-900/20'
                        : 'border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800'
                    }`}
                  >
                    <Text className={`font-rubik-medium ${
                      attendanceStatus === status
                        ? 'text-secondary-700 dark:text-secondary-300'
                        : 'text-neutral-700 dark:text-neutral-300'
                    }`}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View className="mb-6">
              <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                Notes (Optional)
              </Text>
              <TextInput
                value={attendanceNotes}
                onChangeText={setAttendanceNotes}
                placeholder="Add any notes..."
                multiline
                numberOfLines={3}
                className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
              />
            </View>

            <TouchableOpacity
              onPress={handleMarkAttendance}
              className="w-full py-4 bg-secondary-500 rounded-xl"
            >
              <Text className="text-white text-center font-rubik-semibold text-lg">
                Mark Attendance
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}
