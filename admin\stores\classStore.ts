import { create } from 'zustand'
import { createClient } from '@/utils/supabase/client'
import { Class, Teacher } from '@/lib/supabase'

interface ClassFormData {
  className: string
  grade: string
  section?: string
  subject?: string
  academicYear: string
}

interface ClassWithTeacher extends Class {
  teachers?: Teacher[]
  studentCount?: number
}

interface ClassStore {
  // State
  classes: ClassWithTeacher[]
  loading: boolean
  error: string | null

  // Actions
  fetchClasses: () => Promise<void>
  addClass: (classData: ClassFormData) => Promise<void>
  updateClass: (id: string, updates: Partial<ClassFormData>) => Promise<void>
  deleteClass: (id: string) => Promise<void>
  clearError: () => void
}

export const useClassStore = create<ClassStore>((set, get) => ({
  // Initial state
  classes: [],
  loading: false,
  error: null,

  // Fetch all classes for the current school
  fetchClasses: async () => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      // Get user's school
      const { data: adminData } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      if (!adminData) {
        throw new Error('User is not a school admin')
      }

      // Fetch classes with teacher assignments and student counts
      const { data: classesData, error: classesError } = await supabase
        .from('classes')
        .select(`
          *,
          teacher_classes(
            teacher:teachers(
              id,
              name,
              teacher_id,
              email,
              subject
            )
          ),
          student_classes(count)
        `)
        .eq('school_id', adminData.school_id)
        .eq('is_active', true)
        .order('grade', { ascending: true })
        .order('class_name', { ascending: true })

      if (classesError) {
        throw classesError
      }

      // Transform the data
      const transformedClasses: ClassWithTeacher[] = (classesData || []).map(classItem => ({
        ...classItem,
        teachers: classItem.teacher_classes?.map((tc: any) => tc.teacher) || [],
        studentCount: classItem.student_classes?.length || 0
      }))

      set({ classes: transformedClasses, loading: false })
    } catch (error) {
      console.error('Error fetching classes:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch classes',
        loading: false 
      })
    }
  },

  // Add a new class
  addClass: async (classData: ClassFormData) => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      // Get user's school
      const { data: adminData } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      if (!adminData) {
        throw new Error('User is not a school admin')
      }

      // Insert the new class
      const { error: insertError } = await supabase
        .from('classes')
        .insert([{
          school_id: adminData.school_id,
          class_name: classData.className,
          grade: classData.grade,
          section: classData.section,
          subject: classData.subject,
          academic_year: classData.academicYear,
          is_active: true
        }])

      if (insertError) {
        throw insertError
      }

      // Refresh classes list
      await get().fetchClasses()
      set({ loading: false })
    } catch (error) {
      console.error('Error adding class:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to add class',
        loading: false 
      })
    }
  },

  // Update an existing class
  updateClass: async (id: string, updates: Partial<ClassFormData>) => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()

      const updateData: any = {
        updated_at: new Date().toISOString()
      }

      if (updates.className) updateData.class_name = updates.className
      if (updates.grade) updateData.grade = updates.grade
      if (updates.section !== undefined) updateData.section = updates.section
      if (updates.subject !== undefined) updateData.subject = updates.subject
      if (updates.academicYear) updateData.academic_year = updates.academicYear

      const { error: updateError } = await supabase
        .from('classes')
        .update(updateData)
        .eq('id', id)

      if (updateError) {
        throw updateError
      }

      // Refresh classes list
      await get().fetchClasses()
      set({ loading: false })
    } catch (error) {
      console.error('Error updating class:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update class',
        loading: false 
      })
    }
  },

  // Delete a class (soft delete by setting is_active to false)
  deleteClass: async (id: string) => {
    set({ loading: true, error: null })
    
    try {
      const supabase = createClient()

      // Check if class has students enrolled
      const { data: studentClasses, error: checkError } = await supabase
        .from('student_classes')
        .select('id')
        .eq('class_id', id)
        .eq('is_active', true)

      if (checkError) {
        throw checkError
      }

      if (studentClasses && studentClasses.length > 0) {
        throw new Error('Cannot delete class with enrolled students. Please move students to other classes first.')
      }

      // Soft delete the class
      const { error: deleteError } = await supabase
        .from('classes')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)

      if (deleteError) {
        throw deleteError
      }

      // Refresh classes list
      await get().fetchClasses()
      set({ loading: false })
    } catch (error) {
      console.error('Error deleting class:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete class',
        loading: false 
      })
    }
  },

  // Clear error
  clearError: () => {
    set({ error: null })
  }
}))
