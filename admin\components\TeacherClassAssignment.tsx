'use client'

import React, { useState, useEffect } from 'react'
import { createClient } from '@/utils/supabase/client'
import { Teacher, Class } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Users, 
  BookOpen, 
  Plus, 
  Trash2, 
  UserCheck,
  AlertCircle
} from 'lucide-react'

interface TeacherWithClasses extends Teacher {
  classes: Class[]
}

interface ClassWithTeachers extends Class {
  teachers: Teacher[]
}

export default function TeacherClassAssignment() {
  const [teachers, setTeachers] = useState<TeacherWithClasses[]>([])
  const [classes, setClasses] = useState<ClassWithTeachers[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false)
  const [selectedTeacher, setSelectedTeacher] = useState<string>('')
  const [selectedClasses, setSelectedClasses] = useState<string[]>([])

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      
      // Get current user's school
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      const { data: adminData } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user.id)
        .single()

      if (!adminData) {
        throw new Error('User is not a school admin')
      }

      // Fetch teachers with their assigned classes
      const { data: teachersData, error: teachersError } = await supabase
        .from('teachers')
        .select(`
          *,
          teacher_classes(
            class:classes(*)
          )
        `)
        .eq('school_id', adminData.school_id)
        .eq('is_active', true)

      if (teachersError) throw teachersError

      // Fetch classes with their assigned teachers
      const { data: classesData, error: classesError } = await supabase
        .from('classes')
        .select(`
          *,
          teacher_classes(
            teacher:teachers(*)
          )
        `)
        .eq('school_id', adminData.school_id)
        .eq('is_active', true)

      if (classesError) throw classesError

      // Transform data
      const transformedTeachers: TeacherWithClasses[] = (teachersData || []).map(teacher => ({
        ...teacher,
        classes: teacher.teacher_classes?.map((tc: any) => tc.class) || []
      }))

      const transformedClasses: ClassWithTeachers[] = (classesData || []).map(classItem => ({
        ...classItem,
        teachers: classItem.teacher_classes?.map((tc: any) => tc.teacher) || []
      }))

      setTeachers(transformedTeachers)
      setClasses(transformedClasses)
    } catch (error) {
      console.error('Error fetching data:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }

  const handleAssignClasses = async () => {
    if (!selectedTeacher || selectedClasses.length === 0) {
      setError('Please select a teacher and at least one class')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      
      // Get current user's school
      const { data: { user } } = await supabase.auth.getUser()
      const { data: adminData } = await supabase
        .from('school_admins')
        .select('school_id')
        .eq('user_id', user?.id)
        .single()

      // Create teacher-class assignments
      const assignments = selectedClasses.map(classId => ({
        teacher_id: selectedTeacher,
        class_id: classId,
        school_id: adminData?.school_id,
        is_primary: false // You could add logic to determine primary teacher
      }))

      const { error: insertError } = await supabase
        .from('teacher_classes')
        .upsert(assignments, {
          onConflict: 'teacher_id,class_id'
        })

      if (insertError) throw insertError

      // Refresh data
      await fetchData()
      
      // Reset form
      setSelectedTeacher('')
      setSelectedClasses([])
      setIsAssignDialogOpen(false)
    } catch (error) {
      console.error('Error assigning classes:', error)
      setError(error instanceof Error ? error.message : 'Failed to assign classes')
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveAssignment = async (teacherId: string, classId: string) => {
    if (!window.confirm('Are you sure you want to remove this class assignment?')) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()

      const { error } = await supabase
        .from('teacher_classes')
        .delete()
        .eq('teacher_id', teacherId)
        .eq('class_id', classId)

      if (error) throw error

      // Refresh data
      await fetchData()
    } catch (error) {
      console.error('Error removing assignment:', error)
      setError(error instanceof Error ? error.message : 'Failed to remove assignment')
    } finally {
      setLoading(false)
    }
  }

  const handleClassSelection = (classId: string, checked: boolean) => {
    if (checked) {
      setSelectedClasses(prev => [...prev, classId])
    } else {
      setSelectedClasses(prev => prev.filter(id => id !== classId))
    }
  }

  const getAvailableClasses = () => {
    if (!selectedTeacher) return classes

    const teacher = teachers.find(t => t.id === selectedTeacher)
    const assignedClassIds = teacher?.classes.map(c => c.id) || []
    
    return classes.filter(c => !assignedClassIds.includes(c.id))
  }

  if (loading && teachers.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading assignments...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-destructive/15 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-destructive">{error}</p>
            <Button variant="ghost" size="sm" onClick={() => setError(null)}>
              Dismiss
            </Button>
          </div>
        </div>
      )}

      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Teacher-Class Assignments
              </CardTitle>
              <CardDescription>
                Assign teachers to classes and manage their responsibilities
              </CardDescription>
            </div>
            <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Assign Classes
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Assign Classes to Teacher</DialogTitle>
                  <DialogDescription>
                    Select a teacher and the classes they should teach
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Select Teacher</label>
                    <Select value={selectedTeacher} onValueChange={setSelectedTeacher}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a teacher" />
                      </SelectTrigger>
                      <SelectContent>
                        {teachers.map(teacher => (
                          <SelectItem key={teacher.id} value={teacher.id}>
                            {teacher.name} ({teacher.teacher_id})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedTeacher && (
                    <div>
                      <label className="text-sm font-medium mb-2 block">Select Classes</label>
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {getAvailableClasses().map(classItem => (
                          <div key={classItem.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={classItem.id}
                              checked={selectedClasses.includes(classItem.id)}
                              onCheckedChange={(checked) => 
                                handleClassSelection(classItem.id, checked as boolean)
                              }
                            />
                            <label htmlFor={classItem.id} className="text-sm">
                              {classItem.class_name} - Grade {classItem.grade}
                              {classItem.section && ` (${classItem.section})`}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end gap-2 pt-4">
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setIsAssignDialogOpen(false)
                        setSelectedTeacher('')
                        setSelectedClasses([])
                      }}
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleAssignClasses}
                      disabled={!selectedTeacher || selectedClasses.length === 0 || loading}
                    >
                      {loading ? 'Assigning...' : 'Assign Classes'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* Teachers and their assignments */}
      {teachers.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2" />
              <p>No teachers found</p>
              <p className="text-sm">Add teachers to start assigning classes</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {teachers.map(teacher => (
            <Card key={teacher.id}>
              <CardHeader>
                <CardTitle className="text-lg">{teacher.name}</CardTitle>
                <CardDescription>
                  {teacher.teacher_id} • {teacher.subject || 'No subject specified'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {teacher.classes.length === 0 ? (
                  <div className="text-center text-muted-foreground py-4">
                    <BookOpen className="h-6 w-6 mx-auto mb-2" />
                    <p className="text-sm">No classes assigned</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {teacher.classes.map(classItem => (
                      <div key={classItem.id} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium text-sm">{classItem.class_name}</div>
                          <div className="text-xs text-muted-foreground">
                            Grade {classItem.grade}
                            {classItem.section && ` - Section ${classItem.section}`}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveAssignment(teacher.id, classItem.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
