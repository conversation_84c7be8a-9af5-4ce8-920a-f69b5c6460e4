'use client'

import React, { useState } from 'react'
import ClassManagement from '@/components/ClassManagement'
import TeacherClassAssignment from '@/components/TeacherClassAssignment'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { BookOpen, UserCheck } from 'lucide-react'

type TabType = 'classes' | 'assignments'

export default function ClassesPage() {
  const [activeTab, setActiveTab] = useState<TabType>('classes')

  const tabs = [
    {
      id: 'classes' as TabType,
      label: 'Manage Classes',
      icon: BookOpen,
      description: 'Create and manage classes for your school'
    },
    {
      id: 'assignments' as TabType,
      label: 'Teacher Assignments',
      icon: UserCheck,
      description: 'Assign teachers to classes and manage responsibilities'
    }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Class Management</h1>
        <p className="text-muted-foreground">
          Comprehensive class and teacher assignment management
        </p>
      </div>

      {/* Tab Navigation */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-2">
            {tabs.map(tab => {
              const Icon = tab.icon
              return (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? 'default' : 'outline'}
                  onClick={() => setActiveTab(tab.id)}
                  className="flex items-center gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </Button>
              )
            })}
          </div>
          <div className="mt-4 text-sm text-muted-foreground">
            {tabs.find(tab => tab.id === activeTab)?.description}
          </div>
        </CardContent>
      </Card>

      {/* Tab Content */}
      {activeTab === 'classes' && <ClassManagement />}
      {activeTab === 'assignments' && <TeacherClassAssignment />}
    </div>
  )
}
