// Test utilities for Zustand stores
import { useAuthStore } from '@/stores/authStore';
import { useStudentStore } from '@/stores/studentStore';

export const testAuthStore = () => {
  const { user, isLoading, error, signIn, signOut, changePassword, checkSession } = useAuthStore.getState();
  
  console.log('Auth Store State:', {
    user,
    isLoading,
    error,
    hasSignIn: typeof signIn === 'function',
    hasSignOut: typeof signOut === 'function',
    hasChangePassword: typeof changePassword === 'function',
    hasCheckSession: typeof checkSession === 'function',
  });
  
  return {
    user,
    isLoading,
    error,
    actions: {
      signIn,
      signOut,
      changePassword,
      checkSession,
    }
  };
};

export const testStudentStore = () => {
  const { 
    students, 
    currentStudent, 
    attendance, 
    isLoading, 
    error,
    fetchStudents,
    fetchCurrentStudent,
    fetchStudentAttendance,
    updateStudentGrade,
    markAttendance
  } = useStudentStore.getState();
  
  console.log('Student Store State:', {
    studentsCount: students.length,
    currentStudent: currentStudent?.name || 'None',
    attendanceCount: attendance.length,
    isLoading,
    error,
    hasFetchStudents: typeof fetchStudents === 'function',
    hasFetchCurrentStudent: typeof fetchCurrentStudent === 'function',
    hasFetchStudentAttendance: typeof fetchStudentAttendance === 'function',
    hasUpdateStudentGrade: typeof updateStudentGrade === 'function',
    hasMarkAttendance: typeof markAttendance === 'function',
  });
  
  return {
    students,
    currentStudent,
    attendance,
    isLoading,
    error,
    actions: {
      fetchStudents,
      fetchCurrentStudent,
      fetchStudentAttendance,
      updateStudentGrade,
      markAttendance,
    }
  };
};

// Test Supabase connection
export const testSupabaseConnection = async () => {
  try {
    const { supabase } = await import('@/lib/supabase');
    
    // Test basic connection
    const { data, error } = await supabase
      .from('students')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('Supabase connection error:', error);
      return { success: false, error: error.message };
    }
    
    console.log('Supabase connection successful');
    return { success: true, data };
  } catch (error: any) {
    console.error('Supabase setup error:', error);
    return { success: false, error: error.message };
  }
};

// Test authentication flow
export const testAuthFlow = async (email: string, password: string, role: 'student' | 'teacher') => {
  try {
    const authStore = useAuthStore.getState();
    
    console.log('Testing auth flow for:', { email, role });
    
    // Test sign in
    await authStore.signIn(email, password, role);
    
    const { user } = useAuthStore.getState();
    
    if (user) {
      console.log('Auth test successful:', {
        userId: user.id,
        email: user.email,
        role: user.role,
        profileName: user.profile?.name,
      });
      return { success: true, user };
    } else {
      console.log('Auth test failed: No user returned');
      return { success: false, error: 'No user returned' };
    }
  } catch (error: any) {
    console.error('Auth test error:', error);
    return { success: false, error: error.message };
  }
};
