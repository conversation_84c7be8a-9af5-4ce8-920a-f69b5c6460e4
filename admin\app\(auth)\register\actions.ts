'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'

export async function registerSchool(formData: FormData) {
  const supabase = await createClient()

  // Extract form data
  const schoolName = formData.get('schoolName') as string
  const schoolEmail = formData.get('schoolEmail') as string
  const schoolPhone = formData.get('schoolPhone') as string
  const schoolAddress = formData.get('schoolAddress') as string
  const adminName = formData.get('adminName') as string
  const adminEmail = formData.get('adminEmail') as string
  const adminPassword = formData.get('adminPassword') as string
  const confirmPassword = formData.get('confirmPassword') as string

  // Basic validation
  if (!schoolName || !schoolEmail || !adminName || !adminEmail || !adminPassword) {
    redirect('/error?message=Missing required fields')
  }

  if (adminPassword !== confirmPassword) {
    redirect('/error?message=Passwords do not match')
  }

  if (adminPassword.length < 8) {
    redirect('/error?message=Password must be at least 8 characters long')
  }

  try {
    // First, create the user account
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: adminEmail,
      password: adminPassword,
      options: {
        data: {
          name: adminName,
          role: 'school_admin'
        }
      }
    })

    if (authError) {
      console.error('Error creating user:', authError)
      redirect('/error?message=' + encodeURIComponent(authError.message))
    }

    if (!authData.user) {
      redirect('/error?message=Failed to create user account')
    }

    // Create the school
    const { data: schoolData, error: schoolError } = await supabase
      .from('schools')
      .insert([{
        name: schoolName,
        email: schoolEmail,
        phone: schoolPhone || null,
        address: schoolAddress || null,
      }])
      .select()
      .single()

    if (schoolError) {
      console.error('Error creating school:', schoolError)
      redirect('/error?message=' + encodeURIComponent(schoolError.message))
    }

    // Link the user to the school as primary admin
    const { error: adminError } = await supabase
      .from('school_admins')
      .insert([{
        user_id: authData.user.id,
        school_id: schoolData.id,
        role: 'admin',
        is_primary: true
      }])

    if (adminError) {
      console.error('Error creating school admin:', adminError)
      redirect('/error?message=' + encodeURIComponent(adminError.message))
    }

    revalidatePath('/', 'layout')
    redirect('/login?message=' + encodeURIComponent('Registration successful! Please check your email to verify your account.'))
  } catch (error) {
    console.error('Registration error:', error)
    redirect('/error?message=An unexpected error occurred during registration')
  }
}
