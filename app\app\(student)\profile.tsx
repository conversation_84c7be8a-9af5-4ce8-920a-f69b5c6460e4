import React, { useEffect, useState } from 'react';
import { ScrollView, Text, View, RefreshControl, TouchableOpacity, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuthStore } from '@/stores/authStore';
import { useStudentStore } from '@/stores/studentStore';

export default function StudentProfile() {
  const router = useRouter();
  const { user, logout } = useAuthStore();
  const { 
    currentStudent, 
    enrollments, 
    isLoading, 
    error, 
    fetchCurrentStudent, 
    fetchStudentClasses,
    clearError 
  } = useStudentStore();
  
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user?.role !== 'student') {
      Alert.alert('Access Denied', 'This page is only accessible to students.');
      router.back();
      return;
    }
    
    loadStudentData();
  }, [user]);

  const loadStudentData = async () => {
    try {
      await fetchCurrentStudent();
      if (currentStudent?.id) {
        await fetchStudentClasses(currentStudent.id);
      }
    } catch (error) {
      console.error('Error loading student data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadStudentData();
    setRefreshing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout },
      ]
    );
  };

  if (error) {
    return (
      <View className="flex-1 bg-light-background dark:bg-dark-background">
        <View className="bg-primary-500 pt-12 pb-6 px-6">
          <Text className="text-white text-2xl font-rubik-bold">My Profile</Text>
        </View>
        
        <View className="flex-1 justify-center items-center px-6">
          <View className="bg-red-50 dark:bg-red-900/20 rounded-xl p-6 w-full">
            <Text className="text-red-800 dark:text-red-200 font-rubik-bold text-center mb-2">
              Error Loading Profile
            </Text>
            <Text className="text-red-700 dark:text-red-300 font-rubik text-center mb-4">
              {error}
            </Text>
            <TouchableOpacity
              onPress={() => {
                clearError();
                loadStudentData();
              }}
              className="bg-red-600 rounded-lg py-3"
            >
              <Text className="text-white font-rubik-semibold text-center">Try Again</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <ScrollView 
      className="flex-1 bg-light-background dark:bg-dark-background"
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View className="bg-primary-500 pt-12 pb-6 px-6">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-white text-2xl font-rubik-bold">My Profile</Text>
            <Text className="text-primary-100 font-rubik">
              {currentStudent?.name || 'Student Dashboard'}
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleLogout}
            className="bg-white/20 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-rubik-medium">Logout</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View className="px-6 py-6">
        {/* Personal Information */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Personal Information
          </Text>
          
          {currentStudent ? (
            <View className="space-y-3">
              <View>
                <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                  Full Name
                </Text>
                <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                  {currentStudent.name}
                </Text>
              </View>
              
              <View>
                <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                  Student ID
                </Text>
                <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                  {currentStudent.student_id || 'Not assigned'}
                </Text>
              </View>
              
              <View>
                <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                  Email
                </Text>
                <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                  {currentStudent.email}
                </Text>
              </View>
              
              <View>
                <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                  Grade
                </Text>
                <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                  {currentStudent.grade || 'Not assigned'}
                </Text>
              </View>
              
              {currentStudent.phone && (
                <View>
                  <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                    Phone
                  </Text>
                  <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                    {currentStudent.phone}
                  </Text>
                </View>
              )}
              
              {currentStudent.enrollment_date && (
                <View>
                  <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                    Enrollment Date
                  </Text>
                  <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                    {new Date(currentStudent.enrollment_date).toLocaleDateString()}
                  </Text>
                </View>
              )}
            </View>
          ) : (
            <View className="py-8">
              <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center">
                {isLoading ? 'Loading profile...' : 'No profile information available'}
              </Text>
            </View>
          )}
        </View>

        {/* Enrolled Classes */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            My Classes
          </Text>
          
          {enrollments && enrollments.length > 0 ? (
            <View className="space-y-3">
              {enrollments.map((enrollment) => (
                <View 
                  key={enrollment.id} 
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                >
                  <View className="flex-row justify-between items-start">
                    <View className="flex-1">
                      <Text className="font-rubik-bold text-neutral-900 dark:text-neutral-100">
                        {enrollment.class?.name || enrollment.class?.class_name}
                      </Text>
                      <Text className="text-sm text-neutral-600 dark:text-neutral-400 font-rubik">
                        {enrollment.class?.subject}
                      </Text>
                      {enrollment.class?.grade_level && (
                        <Text className="text-sm text-neutral-500 dark:text-neutral-500 font-rubik">
                          Grade: {enrollment.class.grade_level}
                        </Text>
                      )}
                      {enrollment.class?.room_number && (
                        <Text className="text-sm text-neutral-500 dark:text-neutral-500 font-rubik">
                          Room: {enrollment.class.room_number}
                        </Text>
                      )}
                    </View>
                    <View className="bg-green-100 dark:bg-green-900/30 px-3 py-1 rounded-full">
                      <Text className="text-green-800 dark:text-green-200 text-xs font-rubik-medium">
                        {enrollment.status}
                      </Text>
                    </View>
                  </View>
                  
                  {enrollment.enrollment_date && (
                    <Text className="text-xs text-neutral-400 dark:text-neutral-500 font-rubik mt-2">
                      Enrolled: {new Date(enrollment.enrollment_date).toLocaleDateString()}
                    </Text>
                  )}
                </View>
              ))}
            </View>
          ) : (
            <View className="py-8">
              <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center">
                {isLoading ? 'Loading classes...' : 'No classes enrolled yet'}
              </Text>
            </View>
          )}
        </View>

        {/* Contact Information */}
        {currentStudent && (currentStudent.parent_contact || currentStudent.emergency_contact) && (
          <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Emergency Contacts
            </Text>
            
            <View className="space-y-3">
              {currentStudent.parent_contact && (
                <View>
                  <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                    Parent/Guardian
                  </Text>
                  <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                    {currentStudent.parent_contact}
                  </Text>
                  {currentStudent.parent_email && (
                    <Text className="text-sm font-rubik text-neutral-600 dark:text-neutral-400">
                      {currentStudent.parent_email}
                    </Text>
                  )}
                </View>
              )}
              
              {currentStudent.emergency_contact && (
                <View>
                  <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">
                    Emergency Contact
                  </Text>
                  <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                    {currentStudent.emergency_contact}
                  </Text>
                  {currentStudent.emergency_phone && (
                    <Text className="text-sm font-rubik text-neutral-600 dark:text-neutral-400">
                      {currentStudent.emergency_phone}
                    </Text>
                  )}
                </View>
              )}
            </View>
          </View>
        )}

        {/* Quick Actions */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Quick Actions
          </Text>
          
          <View className="space-y-3">
            <TouchableOpacity 
              onPress={() => router.push('/(student)/attendance')}
              className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex-row items-center"
            >
              <Text className="text-2xl mr-3">📊</Text>
              <View>
                <Text className="font-rubik-semibold text-blue-800 dark:text-blue-200">
                  View My Attendance
                </Text>
                <Text className="text-sm font-rubik text-blue-600 dark:text-blue-300">
                  Check attendance records and statistics
                </Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity 
              onPress={() => router.push('/(student)/grades')}
              className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 flex-row items-center"
            >
              <Text className="text-2xl mr-3">📚</Text>
              <View>
                <Text className="font-rubik-semibold text-green-800 dark:text-green-200">
                  View My Grades
                </Text>
                <Text className="text-sm font-rubik text-green-600 dark:text-green-300">
                  Check grades and academic progress
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
