/**
 * Simple test script to verify the teacher attendance implementation
 */

import { LocationService } from '@/utils/locationService';
import { SecurityService } from '@/utils/securityService';

// Test data
const mockGeofence = {
  id: 'test-geofence',
  school_id: 'test-school',
  name: 'Test School',
  center_latitude: 40.7128,
  center_longitude: -74.0060,
  radius_meters: 100,
  is_active: true,
  allowed_check_in_start: '07:00:00',
  allowed_check_in_end: '09:00:00',
  allowed_check_out_start: '15:00:00',
  allowed_check_out_end: '18:00:00',
  allowed_days: [1, 2, 3, 4, 5]
};

const mockSettings = {
  id: 'test-settings',
  school_id: 'test-school',
  earliest_check_in: '06:00:00',
  latest_check_in: '10:00:00',
  earliest_check_out: '14:00:00',
  latest_check_out: '20:00:00',
  require_location: true,
  max_location_accuracy_meters: 50,
  geofence_buffer_meters: 100,
  max_daily_attempts: 5,
  lockout_duration_minutes: 30,
  require_photo_verification: false,
  allow_manual_override: true,
  notify_admin_on_suspicious: true,
  notify_teacher_on_location_fail: true,
  minimum_work_hours: 8.0,
  break_time_minutes: 60
};

export async function testImplementation() {
  console.log('🧪 Testing Teacher Attendance Implementation...\n');

  try {
    // Test 1: Location Service
    console.log('1️⃣ Testing Location Service...');
    const locationService = LocationService.getInstance();
    
    const testLocation = {
      latitude: 40.7128,
      longitude: -74.0060,
      accuracy: 10,
      timestamp: Date.now()
    };

    const geofenceCheck = locationService.isWithinGeofence(testLocation, mockGeofence);
    console.log(`   ✅ Geofence check: ${geofenceCheck.withinGeofence ? 'PASS' : 'FAIL'}`);
    console.log(`   📏 Distance: ${geofenceCheck.distance.toFixed(2)}m\n`);

    // Test 2: Security Service
    console.log('2️⃣ Testing Security Service...');
    const securityService = SecurityService.getInstance();
    
    const deviceFingerprint = await securityService.generateDeviceFingerprint();
    console.log(`   ✅ Device fingerprint generated`);
    console.log(`   📱 Device ID: ${deviceFingerprint.deviceId}`);
    console.log(`   🔧 Device Type: ${deviceFingerprint.deviceType}`);
    console.log(`   📦 App Version: ${deviceFingerprint.appVersion}\n`);

    // Test 3: Security Validation
    console.log('3️⃣ Testing Security Validation...');
    const validation = await securityService.validateAttendanceAttempt(
      'test-teacher-123',
      mockSettings,
      testLocation
    );
    
    console.log(`   ✅ Validation result: ${validation.isValid ? 'VALID' : 'INVALID'}`);
    console.log(`   📊 Risk score: ${validation.riskScore}/100`);
    console.log(`   ⚠️  Violations: ${validation.violations.length}`);
    
    if (validation.violations.length > 0) {
      validation.violations.forEach((violation, index) => {
        console.log(`      ${index + 1}. ${violation.severity.toUpperCase()}: ${violation.message}`);
      });
    }
    console.log('');

    // Test 4: Distance Calculation
    console.log('4️⃣ Testing Distance Calculation...');
    const distance = locationService.calculateDistance(
      40.7128, -74.0060, // NYC
      40.7589, -73.9851  // Times Square
    );
    console.log(`   ✅ Distance NYC to Times Square: ${distance.toFixed(2)}m\n`);

    // Test 5: Time Window Check
    console.log('5️⃣ Testing Time Window...');
    const timeCheck = locationService.isWithinTimeWindow(mockGeofence, true);
    console.log(`   ✅ Current time window: ${timeCheck ? 'VALID' : 'INVALID'}\n`);

    // Test 6: Attempt Statistics
    console.log('6️⃣ Testing Attempt Statistics...');
    const stats = securityService.getAttemptStatistics('test-teacher-123');
    console.log(`   📈 Total attempts: ${stats.totalAttempts}`);
    console.log(`   ✅ Successful: ${stats.successfulAttempts}`);
    console.log(`   ❌ Failed: ${stats.failedAttempts}`);
    console.log(`   📅 Today: ${stats.todayAttempts}`);
    console.log(`   🔒 Locked out: ${stats.isLockedOut ? 'YES' : 'NO'}\n`);

    console.log('🎉 All tests completed successfully!');
    console.log('✅ Implementation is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('🔧 Please check the implementation for issues.');
  }
}

// Export for use in other files
export default testImplementation;

// Console test runner
if (typeof window === 'undefined') {
  // Running in Node.js environment
  console.log('Running tests in Node.js environment...');
  // testImplementation();
}
