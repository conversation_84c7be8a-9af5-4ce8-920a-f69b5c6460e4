import {
  SchoolGeofence,
  TeacherAttendance,
  TeacherAttendanceSettings,
  TeacherAttendanceSuspiciousActivity,
  supabase
} from '@/lib/supabase';
import { DeviceInfo, LocationService, LocationVerificationResult } from '@/utils/locationService';
import { SecurityService } from '@/utils/securityService';
import { create } from 'zustand';

interface TeacherAttendanceState {
  // State
  todayAttendance: TeacherAttendance | null;
  attendanceHistory: TeacherAttendance[];
  geofences: SchoolGeofence[];
  settings: TeacherAttendanceSettings | null;
  suspiciousActivities: TeacherAttendanceSuspiciousActivity[];
  isLoading: boolean;
  error: string | null;
  
  // Services
  locationService: LocationService;
  securityService: SecurityService;
  
  // Actions
  fetchTodayAttendance: (teacherId: string) => Promise<void>;
  fetchAttendanceHistory: (teacherId: string, limit?: number) => Promise<void>;
  fetchGeofences: (schoolId: string) => Promise<void>;
  fetchSettings: (schoolId: string) => Promise<void>;
  fetchSuspiciousActivities: (teacherId: string) => Promise<void>;
  
  // Attendance actions
  checkIn: (teacherId: string, schoolId: string) => Promise<void>;
  checkOut: (teacherId: string) => Promise<void>;
  
  // Utility actions
  clearError: () => void;
  reset: () => void;
}

export const useTeacherAttendanceStore = create<TeacherAttendanceState>((set, get) => ({
  // Initial state
  todayAttendance: null,
  attendanceHistory: [],
  geofences: [],
  settings: null,
  suspiciousActivities: [],
  isLoading: false,
  error: null,
  locationService: LocationService.getInstance(),
  securityService: SecurityService.getInstance(),

  fetchTodayAttendance: async (teacherId: string) => {
    set({ isLoading: true, error: null });
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const { data, error } = await supabase
        .from('teacher_attendance')
        .select('*')
        .eq('teacher_id', teacherId)
        .eq('attendance_date', today)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw new Error(error.message);
      }

      set({ todayAttendance: data || null, isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
    }
  },

  fetchAttendanceHistory: async (teacherId: string, limit = 30) => {
    set({ isLoading: true, error: null });
    try {
      const { data, error } = await supabase
        .from('teacher_attendance')
        .select('*')
        .eq('teacher_id', teacherId)
        .order('attendance_date', { ascending: false })
        .limit(limit);

      if (error) throw new Error(error.message);
      set({ attendanceHistory: data || [], isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
    }
  },

  fetchGeofences: async (schoolId: string) => {
    try {
      const { data, error } = await supabase
        .from('school_geofences')
        .select('*')
        .eq('school_id', schoolId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw new Error(error.message);

      console.log('Fetched geofences:', data);

      if (!data || data.length === 0) {
        console.warn('No active geofences found for school:', schoolId);
        set({ error: 'No geofences configured by admin. Please contact administration to set up school location boundaries.' });
      } else {
        set({ geofences: data, error: null });
      }
    } catch (error: any) {
      console.error('Error fetching geofences:', error);
      set({ error: `Failed to load school configuration: ${error.message}` });
    }
  },

  fetchSettings: async (schoolId: string) => {
    try {
      const { data, error } = await supabase
        .from('teacher_attendance_settings')
        .select('*')
        .eq('school_id', schoolId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw new Error(error.message);
      }

      // Use fetched settings or create default settings
      const settings = data || {
        id: 'default',
        school_id: schoolId,
        earliest_check_in: '06:00:00',
        latest_check_in: '10:00:00',
        earliest_check_out: '14:00:00',
        latest_check_out: '20:00:00',
        require_location: true,
        max_location_accuracy_meters: 50,
        geofence_buffer_meters: 100,
        max_daily_attempts: 5,
        lockout_duration_minutes: 30,
        require_photo_verification: false,
        allow_manual_override: true,
        notify_admin_on_suspicious: true,
        notify_teacher_on_location_fail: true,
        minimum_work_hours: 8.0,
        break_time_minutes: 60,
      };

      set({ settings, error: null });

      if (!data) {
        console.warn('No attendance settings configured by admin, using defaults');
      }
    } catch (error: any) {
      console.error('Error fetching attendance settings:', error);
      set({ error: `Failed to load settings: ${error.message}` });
    }
  },

  fetchSuspiciousActivities: async (teacherId: string) => {
    try {
      const { data, error } = await supabase
        .from('teacher_attendance_suspicious_activity')
        .select('*')
        .eq('teacher_id', teacherId)
        .eq('resolved', false)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw new Error(error.message);
      set({ suspiciousActivities: data || [] });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  checkIn: async (teacherId: string, schoolId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const state = get();
      const { locationService, securityService, geofences, settings } = state;

      // Ensure we have geofences and settings
      if (geofences.length === 0) {
        await state.fetchGeofences(schoolId);
      }
      if (!settings) {
        await state.fetchSettings(schoolId);
      }

      const currentGeofences = get().geofences;
      const currentSettings = get().settings;

      if (currentGeofences.length === 0) {
        throw new Error('No active geofences found for this school. Please contact administration.');
      }

      if (!currentSettings) {
        throw new Error('Attendance settings not configured. Please contact administration.');
      }

      // Perform security validation first
      const securityValidation: any = await securityService.validateAttendanceAttempt(
        teacherId,
        currentSettings
      );

      // Check for critical security violations
      const criticalViolations = securityValidation.violations.filter(
        (v: any) => v.severity === 'critical' || v.severity === 'high'
      );

      if (criticalViolations.length > 0) {
        // Log security violations
        await supabase.from('teacher_attendance_suspicious_activity').insert({
          teacher_id: teacherId,
          activity_type: 'security_violation',
          severity: 'high',
          description: `Security violations detected: ${criticalViolations.map((v: any) => v.message).join(', ')}`,
          metadata: {
            violations: securityValidation.violations,
            riskScore: securityValidation.riskScore,
            deviceFingerprint: securityValidation.deviceFingerprint
          }
        });

        throw new Error(criticalViolations[0].message);
      }

      // Verify location
      const verification: LocationVerificationResult = await locationService.verifyLocationForAttendance(
        currentGeofences,
        currentSettings,
        true // isCheckIn
      );

      // Get device info
      const deviceInfo: DeviceInfo = await locationService.getDeviceInfo();

      // Get location address
      let address = '';
      if (verification.location) {
        address = await locationService.getLocationAddress(
          verification.location.latitude,
          verification.location.longitude
        );
      }

      // Check for suspicious activity
      if (verification.location) {
        const spoofingCheck = locationService.detectLocationSpoofing(verification.location);
        if (spoofingCheck.suspicious) {
          // Log suspicious activity
          await supabase.from('teacher_attendance_suspicious_activity').insert({
            teacher_id: teacherId,
            activity_type: 'location_spoofing',
            severity: 'high',
            description: `Potential location spoofing detected: ${spoofingCheck.reasons.join(', ')}`,
            attempted_latitude: verification.location.latitude,
            attempted_longitude: verification.location.longitude,
            device_id: deviceInfo.deviceId,
            metadata: {
              reasons: spoofingCheck.reasons,
              verification_result: verification,
              device_info: deviceInfo
            }
          });
        }
      }

      // Handle verification failure
      if (!verification.success) {
        let errorMessage = 'Check-in failed: ';
        const issues = [];

        if (!verification.withinGeofence) {
          issues.push('You are not within the school premises');
        }
        if (!verification.securityFlags.withinTimeWindow) {
          issues.push('Check-in is outside allowed time window');
        }
        if (!verification.securityFlags.highAccuracy) {
          issues.push('GPS accuracy is insufficient');
        }
        if (!verification.securityFlags.deviceTrusted) {
          issues.push('Device security check failed');
        }

        errorMessage += issues.join(', ');

        // Log failed attempt
        await supabase.from('teacher_attendance_suspicious_activity').insert({
          teacher_id: teacherId,
          activity_type: 'failed_check_in',
          severity: 'medium',
          description: errorMessage,
          attempted_latitude: verification.location?.latitude,
          attempted_longitude: verification.location?.longitude,
          distance_meters: verification.distanceFromCenter,
          device_id: deviceInfo.deviceId,
          metadata: {
            verification_result: verification,
            device_info: deviceInfo
          }
        });

        throw new Error(errorMessage);
      }

      // Create attendance record
      const attendanceData: Partial<TeacherAttendance> = {
        teacher_id: teacherId,
        school_id: schoolId,
        attendance_date: new Date().toISOString().split('T')[0],
        check_in_time: new Date().toISOString(),
        status: 'present',
        check_in_latitude: verification.location!.latitude,
        check_in_longitude: verification.location!.longitude,
        check_in_accuracy: verification.location!.accuracy,
        check_in_address: address,
        device_id: deviceInfo.deviceId,
        device_type: deviceInfo.deviceType,
        app_version: deviceInfo.appVersion,
        location_verified: true,
        within_geofence: verification.withinGeofence,
      };

      const { data, error } = await supabase
        .from('teacher_attendance')
        .insert(attendanceData)
        .select()
        .single();

      if (error) throw new Error(error.message);

      // Log successful check-in
      await supabase.from('teacher_attendance_audit').insert({
        attendance_id: data.id,
        action: 'check_in',
        new_values: attendanceData,
        location_latitude: verification.location!.latitude,
        location_longitude: verification.location!.longitude,
        device_id: deviceInfo.deviceId,
      });

      set({ todayAttendance: data, isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  checkOut: async (teacherId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const state = get();
      const { locationService, todayAttendance, geofences, settings } = state;

      if (!todayAttendance) {
        throw new Error('No check-in record found for today. Please check in first.');
      }

      if (todayAttendance.check_out_time) {
        throw new Error('You have already checked out for today.');
      }

      // Verify location for check-out
      const verification: LocationVerificationResult = await locationService.verifyLocationForAttendance(
        geofences,
        settings!,
        false // isCheckOut
      );

      // Get device info
      const deviceInfo: DeviceInfo = await locationService.getDeviceInfo();

      // Get location address
      let address = '';
      if (verification.location) {
        address = await locationService.getLocationAddress(
          verification.location.latitude,
          verification.location.longitude
        );
      }

      if (!verification.success) {
        let errorMessage = 'Check-out failed: ';
        const issues = [];

        if (!verification.withinGeofence) {
          issues.push('You are not within the school premises');
        }
        if (!verification.securityFlags.withinTimeWindow) {
          issues.push('Check-out is outside allowed time window');
        }
        if (!verification.securityFlags.highAccuracy) {
          issues.push('GPS accuracy is insufficient');
        }

        errorMessage += issues.join(', ');
        throw new Error(errorMessage);
      }

      // Calculate work hours
      const checkInTime = new Date(todayAttendance.check_in_time!);
      const checkOutTime = new Date();
      const workHours = (checkOutTime.getTime() - checkInTime.getTime()) / (1000 * 60 * 60);

      // Update attendance record
      const updateData = {
        check_out_time: checkOutTime.toISOString(),
        check_out_latitude: verification.location!.latitude,
        check_out_longitude: verification.location!.longitude,
        check_out_accuracy: verification.location!.accuracy,
        check_out_address: address,
        work_hours: Math.round(workHours * 100) / 100, // Round to 2 decimal places
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('teacher_attendance')
        .update(updateData)
        .eq('id', todayAttendance.id)
        .select()
        .single();

      if (error) throw new Error(error.message);

      // Log successful check-out
      await supabase.from('teacher_attendance_audit').insert({
        attendance_id: todayAttendance.id,
        action: 'check_out',
        old_values: { check_out_time: null },
        new_values: updateData,
        location_latitude: verification.location!.latitude,
        location_longitude: verification.location!.longitude,
        device_id: deviceInfo.deviceId,
      });

      set({ todayAttendance: data, isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  clearError: () => set({ error: null }),

  reset: () => set({
    todayAttendance: null,
    attendanceHistory: [],
    geofences: [],
    settings: null,
    suspiciousActivities: [],
    isLoading: false,
    error: null,
  }),
}));
