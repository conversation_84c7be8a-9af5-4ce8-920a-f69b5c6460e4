import * as Location from 'expo-location';
import * as Device from 'expo-device';
import * as Application from 'expo-application';
import { Platform } from 'react-native';
import { SchoolGeofence, TeacherAttendanceSettings } from '@/lib/supabase';

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

export interface LocationVerificationResult {
  success: boolean;
  location?: LocationData;
  withinGeofence: boolean;
  distanceFromCenter?: number;
  error?: string;
  securityFlags: {
    highAccuracy: boolean;
    withinTimeWindow: boolean;
    deviceTrusted: boolean;
    locationEnabled: boolean;
  };
}

export interface DeviceInfo {
  deviceId: string;
  deviceType: string;
  appVersion: string;
  osVersion: string;
  isPhysicalDevice: boolean;
  deviceName?: string;
}

export class LocationService {
  private static instance: LocationService;
  private locationPermissionGranted = false;
  private lastKnownLocation: LocationData | null = null;

  private constructor() {}

  public static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  /**
   * Request location permissions from the user
   */
  public async requestLocationPermissions(): Promise<boolean> {
    try {
      // Check if location services are enabled
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        throw new Error('Location services are disabled. Please enable location services in your device settings.');
      }

      // Request foreground permissions
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      
      if (foregroundStatus !== 'granted') {
        throw new Error('Location permission denied. Please grant location access to mark attendance.');
      }

      this.locationPermissionGranted = true;
      return true;
    } catch (error) {
      console.error('Location permission error:', error);
      throw error;
    }
  }

  /**
   * Get current high-accuracy location
   */
  public async getCurrentLocation(timeoutMs: number = 15000): Promise<LocationData> {
    if (!this.locationPermissionGranted) {
      await this.requestLocationPermissions();
    }

    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.BestForNavigation,
        timeout: timeoutMs,
        maximumAge: 30000, // Accept cached location up to 30 seconds old
      });

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || 0,
        altitude: location.coords.altitude || undefined,
        heading: location.coords.heading || undefined,
        speed: location.coords.speed || undefined,
        timestamp: location.timestamp,
      };

      this.lastKnownLocation = locationData;
      return locationData;
    } catch (error) {
      console.error('Location fetch error:', error);
      throw new Error('Failed to get current location. Please ensure GPS is enabled and try again.');
    }
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  public calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Check if location is within geofence
   */
  public isWithinGeofence(
    location: LocationData,
    geofence: SchoolGeofence
  ): { withinGeofence: boolean; distance: number } {
    const distance = this.calculateDistance(
      location.latitude,
      location.longitude,
      geofence.center_latitude,
      geofence.center_longitude
    );

    return {
      withinGeofence: distance <= geofence.radius_meters,
      distance
    };
  }

  /**
   * Check if current time is within allowed time window
   */
  public isWithinTimeWindow(
    geofence: SchoolGeofence,
    isCheckIn: boolean = true
  ): boolean {
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS format
    const currentDay = now.getDay(); // 0 = Sunday, 6 = Saturday

    // Check if current day is allowed
    if (geofence.allowed_days && !geofence.allowed_days.includes(currentDay)) {
      return false;
    }

    // Check time window
    if (isCheckIn) {
      const startTime = geofence.allowed_check_in_start || '06:00:00';
      const endTime = geofence.allowed_check_in_end || '10:00:00';
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      const startTime = geofence.allowed_check_out_start || '14:00:00';
      const endTime = geofence.allowed_check_out_end || '20:00:00';
      return currentTime >= startTime && currentTime <= endTime;
    }
  }

  /**
   * Get device information for security tracking
   */
  public async getDeviceInfo(): Promise<DeviceInfo> {
    const deviceId = await Application.getAndroidId() || 
                    await Application.getIosIdForVendorAsync() || 
                    'unknown';

    return {
      deviceId,
      deviceType: Platform.OS,
      appVersion: Application.nativeApplicationVersion || 'unknown',
      osVersion: Device.osVersion || 'unknown',
      isPhysicalDevice: Device.isDevice,
      deviceName: Device.deviceName || undefined,
    };
  }

  /**
   * Verify location for attendance with comprehensive security checks
   */
  public async verifyLocationForAttendance(
    geofences: SchoolGeofence[],
    settings: TeacherAttendanceSettings,
    isCheckIn: boolean = true
  ): Promise<LocationVerificationResult> {
    try {
      // Get current location
      const location = await this.getCurrentLocation();
      
      // Check location accuracy
      const highAccuracy = location.accuracy <= (settings.max_location_accuracy_meters || 50);
      
      // Get device info for security
      const deviceInfo = await this.getDeviceInfo();
      const deviceTrusted = deviceInfo.isPhysicalDevice;

      // Find the closest geofence
      let withinGeofence = false;
      let closestDistance = Infinity;
      let withinTimeWindow = false;

      for (const geofence of geofences.filter(g => g.is_active)) {
        const { withinGeofence: inFence, distance } = this.isWithinGeofence(location, geofence);
        const inTimeWindow = this.isWithinTimeWindow(geofence, isCheckIn);

        if (distance < closestDistance) {
          closestDistance = distance;
        }

        if (inFence && inTimeWindow) {
          withinGeofence = true;
          withinTimeWindow = true;
          break;
        } else if (inFence) {
          withinGeofence = true;
        } else if (inTimeWindow) {
          withinTimeWindow = true;
        }
      }

      const securityFlags = {
        highAccuracy,
        withinTimeWindow,
        deviceTrusted,
        locationEnabled: true,
      };

      // Determine overall success
      const success = withinGeofence && 
                     withinTimeWindow && 
                     highAccuracy && 
                     deviceTrusted;

      return {
        success,
        location,
        withinGeofence,
        distanceFromCenter: closestDistance === Infinity ? undefined : closestDistance,
        securityFlags,
      };

    } catch (error: any) {
      return {
        success: false,
        withinGeofence: false,
        error: error.message,
        securityFlags: {
          highAccuracy: false,
          withinTimeWindow: false,
          deviceTrusted: false,
          locationEnabled: false,
        },
      };
    }
  }

  /**
   * Get location address using reverse geocoding
   */
  public async getLocationAddress(latitude: number, longitude: number): Promise<string> {
    try {
      const addresses = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (addresses.length > 0) {
        const address = addresses[0];
        return [
          address.streetNumber,
          address.street,
          address.city,
          address.region,
          address.postalCode,
          address.country
        ].filter(Boolean).join(', ');
      }

      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
  }

  /**
   * Detect potential location spoofing
   */
  public detectLocationSpoofing(location: LocationData): {
    suspicious: boolean;
    reasons: string[];
  } {
    const reasons: string[] = [];
    let suspicious = false;

    // Check for unrealistic accuracy
    if (location.accuracy < 1) {
      reasons.push('Unrealistically high GPS accuracy');
      suspicious = true;
    }

    // Check for impossible speed (if we have previous location)
    if (this.lastKnownLocation) {
      const timeDiff = (location.timestamp - this.lastKnownLocation.timestamp) / 1000; // seconds
      const distance = this.calculateDistance(
        this.lastKnownLocation.latitude,
        this.lastKnownLocation.longitude,
        location.latitude,
        location.longitude
      );
      const speed = distance / timeDiff; // meters per second

      // Flag if speed > 100 m/s (360 km/h) - unrealistic for normal movement
      if (speed > 100 && timeDiff > 10) {
        reasons.push('Impossible movement speed detected');
        suspicious = true;
      }
    }

    // Check for exact coordinate repetition (possible mock location)
    if (this.lastKnownLocation &&
        location.latitude === this.lastKnownLocation.latitude &&
        location.longitude === this.lastKnownLocation.longitude &&
        location.accuracy === this.lastKnownLocation.accuracy) {
      reasons.push('Identical coordinates detected');
      suspicious = true;
    }

    return { suspicious, reasons };
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Get last known location
   */
  public getLastKnownLocation(): LocationData | null {
    return this.lastKnownLocation;
  }

  /**
   * Clear cached location data
   */
  public clearLocationCache(): void {
    this.lastKnownLocation = null;
  }
}
