import { Class, supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/authStore';
import React, { useEffect, useState } from 'react';
import { Alert, FlatList, Modal, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function TeacherClasses() {
  const { user } = useAuthStore();
  const [classes, setClasses] = useState<Class[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [showClassDetailsModal, setShowClassDetailsModal] = useState(false);
  const [students, setStudents] = useState<any[]>([]);
  const [attendanceDate, setAttendanceDate] = useState(new Date().toISOString().split('T')[0]);

  useEffect(() => {
    fetchTeacherClasses();
  }, []);

  const fetchTeacherClasses = async () => {
    if (!user?.id) return;
    
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('teacher_classes')
        .select(`
          classes:class_id (
            id,
            class_name,
            grade,
            section,
            subject,
            academic_year,
            school_id
          )
        `)
        .eq('teacher_id', user.id);

      if (error) throw error;
      
      // Extract the classes from the nested structure
      const teacherClasses = data?.map(item => item.classes) || [];
      setClasses(teacherClasses);
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchClassStudents = async (classId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('student_classes')
        .select(`
          students:student_id (
            id,
            name,
            email,
            grade,
            student_id
          )
        `)
        .eq('class_id', classId);

      if (error) throw error;
      
      // Extract the students from the nested structure
      const classStudents = data?.map(item => item.students) || [];
      setStudents(classStudents);
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClassSelect = async (classItem: Class) => {
    setSelectedClass(classItem);
    await fetchClassStudents(classItem.id);
    setShowClassDetailsModal(true);
  };

  const renderClassCard = ({ item }: { item: Class }) => (
    <TouchableOpacity 
      onPress={() => handleClassSelect(item)}
      className="bg-white dark:bg-neutral-800 rounded-xl p-4 mb-4 shadow-sm"
    >
      <View className="flex-row justify-between items-start mb-2">
        <View>
          <Text className="text-lg font-rubik-bold text-neutral-900 dark:text-neutral-100">
            {item.class_name}
          </Text>
          <Text className="text-sm font-rubik text-neutral-500 dark:text-neutral-400">
            Grade: {item.grade} {item.section ? `| Section: ${item.section}` : ''}
          </Text>
        </View>
        <View className="bg-secondary-50 dark:bg-secondary-900/20 px-3 py-1 rounded-lg">
          <Text className="text-secondary-700 dark:text-secondary-300 font-rubik-medium">
            {item.subject || 'General'}
          </Text>
        </View>
      </View>
      <Text className="text-sm font-rubik text-neutral-600 dark:text-neutral-300">
        Academic Year: {item.academic_year}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View className="flex-1 bg-light-background dark:bg-dark-background">
      {/* Header */}
      <View className="bg-secondary-500 pt-12 pb-6 px-6">
        <Text className="text-white text-2xl font-rubik-bold">
          My Classes
        </Text>
        <Text className="text-secondary-100 font-rubik">
          Manage your class schedules and students
        </Text>
      </View>

      {/* Classes List */}
      <View className="flex-1 px-6 py-6">
        {isLoading ? (
          <View className="flex-1 justify-center items-center">
            <Text className="text-neutral-500 dark:text-neutral-400 font-rubik">
              Loading classes...
            </Text>
          </View>
        ) : classes.length > 0 ? (
          <FlatList
            data={classes}
            renderItem={renderClassCard}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View className="flex-1 justify-center items-center">
            <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center">
              No classes assigned to you
            </Text>
          </View>
        )}
      </View>

      {/* Class Details Modal */}
      <Modal
        visible={showClassDetailsModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-light-background dark:bg-dark-background">
          <View className="flex-row justify-between items-center p-6 border-b border-neutral-200 dark:border-neutral-700">
            <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100">
              {selectedClass?.class_name}
            </Text>
            <TouchableOpacity onPress={() => setShowClassDetailsModal(false)}>
              <Text className="text-secondary-500 font-rubik-medium">Close</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView className="flex-1 p-6">
            {/* Class Info */}
            <View className="bg-white dark:bg-neutral-800 rounded-xl p-4 mb-6 shadow-sm">
              <Text className="text-lg font-rubik-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                Class Information
              </Text>
              <View className="flex flex-col gap-2">
                <View className="flex-row justify-between">
                  <Text className="text-neutral-500 dark:text-neutral-400 font-rubik">Grade:</Text>
                  <Text className="text-neutral-900 dark:text-neutral-100 font-rubik-medium">
                    {selectedClass?.grade} {selectedClass?.section ? `(${selectedClass.section})` : ''}
                  </Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-neutral-500 dark:text-neutral-400 font-rubik">Subject:</Text>
                  <Text className="text-neutral-900 dark:text-neutral-100 font-rubik-medium">
                    {selectedClass?.subject || 'General'}
                  </Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-neutral-500 dark:text-neutral-400 font-rubik">Academic Year:</Text>
                  <Text className="text-neutral-900 dark:text-neutral-100 font-rubik-medium">
                    {selectedClass?.academic_year}
                  </Text>
                </View>
              </View>
            </View>
            
            {/* Attendance Date Picker */}
            <View className="bg-white dark:bg-neutral-800 rounded-xl p-4 mb-6 shadow-sm">
              <Text className="text-lg font-rubik-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                Attendance Date
              </Text>
              <TextInput
                value={attendanceDate}
                onChangeText={setAttendanceDate}
                placeholder="YYYY-MM-DD"
                className="w-full px-4 py-3 bg-neutral-100 dark:bg-neutral-700 rounded-lg text-neutral-900 dark:text-neutral-100 font-rubik"
              />
              <TouchableOpacity 
                className="bg-secondary-500 rounded-lg py-3 mt-3"
                onPress={() => setAttendanceDate(new Date().toISOString().split('T')[0])}
              >
                <Text className="text-white text-center font-rubik-medium">
                  Set to Today
                </Text>
              </TouchableOpacity>
            </View>
            
            {/* Students List */}
            <Text className="text-lg font-rubik-semibold text-neutral-900 dark:text-neutral-100 mb-3">
              Students ({students.length})
            </Text>
            
            {students.length > 0 ? (
              <View className="flex flex-col gap-3">
                {students.map((student) => (
                  <View key={student.id} className="bg-white dark:bg-neutral-800 rounded-xl p-4 shadow-sm">
                    <View className="flex-row justify-between items-start mb-2">
                      <View>
                        <Text className="text-base font-rubik-semibold text-neutral-900 dark:text-neutral-100">
                          {student.name}
                        </Text>
                        <Text className="text-sm font-rubik text-neutral-500 dark:text-neutral-400">
                          ID: {student.student_id || 'N/A'}
                        </Text>
                      </View>
                      <View className="flex-row gap-2">
                        <TouchableOpacity className="bg-success-light px-3 py-1 rounded-lg">
                          <Text className="text-success-dark font-rubik-medium">Present</Text>
                        </TouchableOpacity>
                        <TouchableOpacity className="bg-error-light px-3 py-1 rounded-lg">
                          <Text className="text-error-dark font-rubik-medium">Absent</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 items-center">
                <Text className="text-neutral-500 dark:text-neutral-400 font-rubik text-center">
                  No students enrolled in this class
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}
