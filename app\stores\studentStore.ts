import { Class, Student, StudentEnrollment, supabase } from '@/lib/supabase';
import { create } from 'zustand';

interface StudentState {
  // State
  students: Student[];
  classes: Class[];
  enrollments: StudentEnrollment[];
  currentStudent: Student | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchStudents: (schoolId?: string) => Promise<void>;
  fetchStudentsPaginated: (schoolId: string, page: number, limit: number) => Promise<{ students: Student[], hasMore: boolean }>;
  fetchStudentById: (studentId: string) => Promise<Student | null>;
  fetchCurrentStudent: () => Promise<void>;
  fetchStudentClasses: (studentId: string) => Promise<void>;
  fetchClassStudents: (classId: string) => Promise<void>;
  updateStudent: (studentId: string, updates: Partial<Student>) => Promise<void>;
  enrollStudentInClass: (studentId: string, classId: string) => Promise<void>;
  unenrollStudentFromClass: (studentId: string, classId: string) => Promise<void>;

  // Utility actions
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useStudentStore = create<StudentState>((set, get) => ({
  // Initial state
  students: [],
  classes: [],
  enrollments: [],
  currentStudent: null,
  isLoading: false,
  error: null,

  // Actions
  fetchStudents: async (schoolId?: string) => {
    set({ isLoading: true, error: null });

    try {
      let query = supabase
        .from('students')
        .select(`
          *
        `)
        .eq('is_active', true)
        .order('name');

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      const { data, error } = await query;

      if (error) throw new Error(error.message);

      console.log('Fetched students:', data);
      // Ensure we always set an array, never undefined
      const students = Array.isArray(data) ? data : [];
      set({ students, isLoading: false, error: null });
    } catch (error: any) {
      console.error('Error fetching students:', error);
      // Ensure students array is always initialized even on error
      set({ error: error.message, isLoading: false, students: [] });
    }
  },

  fetchStudentsPaginated: async (schoolId: string, page: number, limit: number) => {
    try {
      const offset = (page - 1) * limit;

      const query = supabase
        .from('students')
        .select('*', { count: 'exact' })
        .eq('school_id', schoolId)
        .eq('is_active', true)
        .order('name')
        .range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) throw new Error(error.message);

      const students = Array.isArray(data) ? data : [];
      const totalCount = count || 0;
      const hasMore = offset + limit < totalCount;

      console.log(`Fetched page ${page}: ${students.length} students, hasMore: ${hasMore}`);

      return { students, hasMore };
    } catch (error: any) {
      console.error('Error fetching paginated students:', error);
      throw error;
    }
  },

  fetchStudentById: async (studentId: string) => {
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from('students')
        .select(`
          *
        `)
        .eq('id', studentId)
        .single();

      if (error) throw new Error(error.message);

      console.log('Fetched student by ID:', data);
      set({ isLoading: false });
      return data;
    } catch (error: any) {
      console.error('Error fetching student by ID:', error);
      set({ error: error.message, isLoading: false });
      return null;
    }
  },

  fetchCurrentStudent: async () => {
    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('No authenticated user found');
      }

      const { data, error } = await supabase
        .from('students')
        .select(`
          *
        `)
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw new Error(error.message);
      }

      console.log('Fetched current student:', data);
      set({ currentStudent: data || null, isLoading: false });
    } catch (error: any) {
      console.error('Error fetching current student:', error);
      set({ error: error.message, isLoading: false, currentStudent: null });
    }
  },

  fetchStudentClasses: async (studentId: string) => {
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from('student_enrollments')
        .select(`
          *
        `)
        .eq('student_id', studentId)
        .eq('status', 'active')
        .order('enrollment_date');

      if (error) throw new Error(error.message);

      const enrollmentData = Array.isArray(data) ? data : [];

      // Get class IDs from enrollments
      const classIds = enrollmentData.map(e => e.class_id).filter(Boolean);

      // Fetch classes separately to avoid RLS recursion
      let classes = [];
      if (classIds.length > 0) {
        const { data: classesData, error: classesError } = await supabase
          .from('classes')
          .select('*')
          .in('id', classIds)
          .eq('is_active', true);

        if (classesError) {
          console.warn('Error fetching classes:', classesError);
        } else {
          classes = classesData || [];
        }
      }

      // Combine enrollment data with class data
      const enrichedEnrollments = enrollmentData.map(enrollment => ({
        ...enrollment,
        class: classes.find(c => c.id === enrollment.class_id)
      }));

      console.log('Fetched student classes:', enrichedEnrollments);
      set({ enrollments: enrichedEnrollments, classes, isLoading: false, error: null });
    } catch (error: any) {
      console.error('Error fetching student classes:', error);
      set({ error: error.message, isLoading: false });
    }
  },

  fetchClassStudents: async (classId: string) => {
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from('student_enrollments')
        .select(`
          *
        `)
        .eq('class_id', classId)
        .eq('status', 'active');

      if (error) throw new Error(error.message);

      const enrollmentData = Array.isArray(data) ? data : [];

      // Get student IDs from enrollments
      const studentIds = enrollmentData.map(e => e.student_id).filter(Boolean);

      // Fetch students separately to avoid RLS recursion
      let students = [];
      if (studentIds.length > 0) {
        const { data: studentsData, error: studentsError } = await supabase
          .from('students')
          .select('*')
          .in('id', studentIds)
          .eq('is_active', true);

        if (studentsError) {
          console.warn('Error fetching students:', studentsError);
        } else {
          students = studentsData || [];
        }
      }

      console.log('Fetched class students:', students);
      set({ students, enrollments: enrollmentData, isLoading: false, error: null });
    } catch (error: any) {
      console.error('Error fetching class students:', error);
      // Ensure arrays are always initialized even on error
      set({ error: error.message, isLoading: false, students: [], enrollments: [] });
    }
  },

  updateStudent: async (studentId: string, updates: Partial<Student>) => {
    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();

      const updateData = {
        ...updates,
        updated_at: new Date().toISOString(),
        updated_by: user?.id,
      };

      const { error } = await supabase
        .from('students')
        .update(updateData)
        .eq('id', studentId);

      if (error) throw new Error(error.message);

      // Update local state
      const state = get();
      const updatedStudents = state.students.map(student =>
        student.id === studentId ? { ...student, ...updateData } : student
      );

      const updatedCurrentStudent = state.currentStudent?.id === studentId
        ? { ...state.currentStudent, ...updateData }
        : state.currentStudent;

      set({
        students: updatedStudents,
        currentStudent: updatedCurrentStudent,
        isLoading: false
      });

      console.log('Student updated successfully');
    } catch (error: any) {
      console.error('Error updating student:', error);
      set({ error: error.message, isLoading: false });
    }
  },

  enrollStudentInClass: async (studentId: string, classId: string) => {
    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();

      const { error } = await supabase
        .from('student_enrollments')
        .insert({
          student_id: studentId,
          class_id: classId,
          enrollment_date: new Date().toISOString().split('T')[0],
          status: 'active',
          enrolled_by: user?.id,
        });

      if (error) throw new Error(error.message);

      console.log('Student enrolled in class successfully');

      // Refresh enrollments
      await get().fetchStudentClasses(studentId);

      set({ isLoading: false });
    } catch (error: any) {
      console.error('Error enrolling student in class:', error);
      set({ error: error.message, isLoading: false });
    }
  },

  unenrollStudentFromClass: async (studentId: string, classId: string) => {
    set({ isLoading: true, error: null });

    try {
      const { error } = await supabase
        .from('student_enrollments')
        .update({ status: 'inactive' })
        .eq('student_id', studentId)
        .eq('class_id', classId);

      if (error) throw new Error(error.message);

      console.log('Student unenrolled from class successfully');

      // Refresh enrollments
      await get().fetchStudentClasses(studentId);

      set({ isLoading: false });
    } catch (error: any) {
      console.error('Error unenrolling student from class:', error);
      set({ error: error.message, isLoading: false });
    }
  },

  // Utility actions
  clearError: () => set({ error: null }),

  setLoading: (loading: boolean) => set({ isLoading: loading }),
}));
