'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/utils/supabase/server'

export interface StudentClassEnrollmentResult {
  success?: boolean
  error?: string
  message?: string
}

export async function enrollStudentInClass(
  studentId: string,
  classId: string
): Promise<StudentClassEnrollmentResult> {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return { error: 'User not authenticated' }
    }

    // Verify user is admin
    const { data: adminData } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (!adminData) {
      return { error: 'Unauthorized: User is not an admin' }
    }

    // Verify student and class belong to the same school
    const { data: studentData } = await supabase
      .from('students')
      .select('school_id')
      .eq('id', studentId)
      .single()

    const { data: classData } = await supabase
      .from('classes')
      .select('school_id')
      .eq('id', classId)
      .single()

    if (!studentData || !classData) {
      return { error: 'Student or class not found' }
    }

    if (studentData.school_id !== adminData.school_id || classData.school_id !== adminData.school_id) {
      return { error: 'Unauthorized: Student or class does not belong to your school' }
    }

    // Check if student is already enrolled in this class
    const { data: existingEnrollment } = await supabase
      .from('student_classes')
      .select('id')
      .eq('student_id', studentId)
      .eq('class_id', classId)
      .eq('is_active', true)
      .single()

    if (existingEnrollment) {
      return { error: 'Student is already enrolled in this class' }
    }

    // Enroll student in class
    const { error: enrollError } = await supabase
      .from('student_classes')
      .insert([{
        student_id: studentId,
        class_id: classId,
        school_id: adminData.school_id,
        enrollment_date: new Date().toISOString().split('T')[0],
        is_active: true
      }])

    if (enrollError) {
      console.error('Error enrolling student:', enrollError)
      return { error: 'Failed to enroll student in class' }
    }

    // Revalidate relevant pages
    revalidatePath('/students')
    revalidatePath('/classes')

    return { 
      success: true, 
      message: 'Student successfully enrolled in class' 
    }

  } catch (error) {
    console.error('Error in enrollStudentInClass:', error)
    return { error: 'An unexpected error occurred' }
  }
}

export async function removeStudentFromClass(
  studentId: string,
  classId: string
): Promise<StudentClassEnrollmentResult> {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return { error: 'User not authenticated' }
    }

    // Verify user is admin
    const { data: adminData } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (!adminData) {
      return { error: 'Unauthorized: User is not an admin' }
    }

    // Remove student from class (soft delete by setting is_active to false)
    const { error: removeError } = await supabase
      .from('student_classes')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('student_id', studentId)
      .eq('class_id', classId)
      .eq('school_id', adminData.school_id)

    if (removeError) {
      console.error('Error removing student from class:', removeError)
      return { error: 'Failed to remove student from class' }
    }

    // Revalidate relevant pages
    revalidatePath('/students')
    revalidatePath('/classes')

    return { 
      success: true, 
      message: 'Student successfully removed from class' 
    }

  } catch (error) {
    console.error('Error in removeStudentFromClass:', error)
    return { error: 'An unexpected error occurred' }
  }
}

export async function getStudentClasses(studentId: string) {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return { error: 'User not authenticated' }
    }

    // Get student's enrolled classes
    const { data, error } = await supabase
      .from('student_classes')
      .select(`
        class:classes(
          id,
          class_name,
          grade,
          section,
          subject,
          academic_year
        ),
        enrollment_date,
        is_active
      `)
      .eq('student_id', studentId)
      .eq('is_active', true)

    if (error) {
      console.error('Error fetching student classes:', error)
      return { error: 'Failed to fetch student classes' }
    }

    return { 
      success: true, 
      classes: data?.map(item => item.class) || [] 
    }

  } catch (error) {
    console.error('Error in getStudentClasses:', error)
    return { error: 'An unexpected error occurred' }
  }
}
