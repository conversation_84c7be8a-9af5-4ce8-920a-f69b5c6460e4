import { AuthUser, supabase } from '@/lib/supabase';
import * as SecureStore from 'expo-secure-store';

// Session storage keys
export const SESSION_KEYS = {
  USER_DATA: 'auth_user_data',
  SESSION_TIMESTAMP: 'auth_session_timestamp',
  USER_ROLE: 'auth_user_role',
  REFRESH_TOKEN: 'auth_refresh_token',
  ACCESS_TOKEN: 'auth_access_token',
} as const;

// Session configuration
export const SESSION_CONFIG = {
  TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  REFRESH_THRESHOLD: 60 * 60 * 1000, // Refresh if expires within 1 hour
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

/**
 * Check if the stored session is still valid based on timestamp
 */
export const isStoredSessionValid = async (): Promise<boolean> => {
  try {
    const timestamp = await SecureStore.getItemAsync(SESSION_KEYS.SESSION_TIMESTAMP);
    if (!timestamp) return false;
    
    const sessionAge = Date.now() - parseInt(timestamp);
    return sessionAge < SESSION_CONFIG.TIMEOUT;
  } catch (error) {
    console.error('Error checking stored session validity:', error);
    return false;
  }
};

/**
 * Store session data securely (optimized for size)
 */
export const storeSessionData = async (user: AuthUser, tokens?: { access_token: string; refresh_token: string }): Promise<void> => {
  try {
    // Store only essential user data to avoid SecureStore size limit (keep under 2048 bytes)
    const essentialUserData = {
      id: user.id,
      email: user.email,
      role: user.role,
      profile: {
        id: user.profile?.id,
        name: user.profile?.name,
        // Only store critical profile fields based on role
        ...(user.role === 'teacher' && {
          school_id: (user.profile as any)?.school_id,
        }),
        ...(user.role === 'student' && {
          student_id: (user.profile as any)?.student_id,
        })
      }
    };

    // Check data size before storing
    const userDataString = JSON.stringify(essentialUserData);
    let finalUserData = essentialUserData;

    if (userDataString.length > 2000) {
      console.warn('User data is large, storing minimal version');
      // Store even more minimal data if needed
      finalUserData = {
        id: user.id,
        email: user.email,
        role: user.role,
        profile: {
          id: user.profile?.id,
          name: user.profile?.name,
        }
      };
    }

    const promises = [
      SecureStore.setItemAsync(SESSION_KEYS.USER_DATA, JSON.stringify(finalUserData)),
      SecureStore.setItemAsync(SESSION_KEYS.SESSION_TIMESTAMP, Date.now().toString()),
      SecureStore.setItemAsync(SESSION_KEYS.USER_ROLE, user.role),
    ];

    if (tokens) {
      promises.push(
        SecureStore.setItemAsync(SESSION_KEYS.ACCESS_TOKEN, tokens.access_token),
        SecureStore.setItemAsync(SESSION_KEYS.REFRESH_TOKEN, tokens.refresh_token)
      );
    }

    await Promise.all(promises);
  } catch (error) {
    console.error('Error storing session data:', error);
    throw new Error('Failed to store session data');
  }
};

/**
 * Retrieve stored session data
 */
export const getStoredSessionData = async (): Promise<AuthUser | null> => {
  try {
    const userData = await SecureStore.getItemAsync(SESSION_KEYS.USER_DATA);
    if (!userData) return null;
    
    return JSON.parse(userData) as AuthUser;
  } catch (error) {
    console.error('Error retrieving session data:', error);
    return null;
  }
};

/**
 * Clear all stored session data
 */
export const clearStoredSessionData = async (): Promise<void> => {
  try {
    const keys = Object.values(SESSION_KEYS);
    await Promise.all(keys.map(key => SecureStore.deleteItemAsync(key)));
  } catch (error) {
    console.error('Error clearing session data:', error);
  }
};

/**
 * Get stored tokens
 */
export const getStoredTokens = async (): Promise<{ access_token: string; refresh_token: string } | null> => {
  try {
    const [accessToken, refreshToken] = await Promise.all([
      SecureStore.getItemAsync(SESSION_KEYS.ACCESS_TOKEN),
      SecureStore.getItemAsync(SESSION_KEYS.REFRESH_TOKEN),
    ]);

    if (!accessToken || !refreshToken) return null;

    return { access_token: accessToken, refresh_token: refreshToken };
  } catch (error) {
    console.error('Error retrieving stored tokens:', error);
    return null;
  }
};

/**
 * Refresh the session with retry logic
 */
export const refreshSession = async (retryCount = 0): Promise<boolean> => {
  try {
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error('Session refresh error:', error);
      
      // Retry logic
      if (retryCount < SESSION_CONFIG.MAX_RETRY_ATTEMPTS) {
        console.log(`Retrying session refresh (attempt ${retryCount + 1})`);
        await new Promise(resolve => setTimeout(resolve, SESSION_CONFIG.RETRY_DELAY * (retryCount + 1)));
        return refreshSession(retryCount + 1);
      }
      
      return false;
    }

    if (data.session) {
      // Store new tokens
      await storeSessionData(
        await getStoredSessionData() as AuthUser,
        {
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
        }
      );
      return true;
    }

    return false;
  } catch (error) {
    console.error('Session refresh failed:', error);
    return false;
  }
};

/**
 * Validate session with Supabase and refresh if needed
 */
export const validateAndRefreshSession = async (): Promise<{ isValid: boolean; user?: AuthUser }> => {
  try {
    // Check if stored session exists and is not expired
    const isStoredValid = await isStoredSessionValid();
    const storedUser = await getStoredSessionData();

    if (!isStoredValid || !storedUser) {
      return { isValid: false };
    }

    // Get current session from Supabase
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Session validation error:', error);
      return { isValid: false };
    }

    if (!session) {
      // Try to refresh the session
      const refreshed = await refreshSession();
      if (refreshed) {
        return { isValid: true, user: storedUser };
      }
      return { isValid: false };
    }

    // Check if session needs refresh (expires soon)
    const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
    const timeUntilExpiry = expiresAt - Date.now();

    if (timeUntilExpiry < SESSION_CONFIG.REFRESH_THRESHOLD) {
      console.log('Session expires soon, refreshing...');
      const refreshed = await refreshSession();
      if (!refreshed) {
        console.warn('Failed to refresh session');
      }
    }

    return { isValid: true, user: storedUser };
  } catch (error) {
    console.error('Session validation failed:', error);
    return { isValid: false };
  }
};

/**
 * Handle session expiration
 */
export const handleSessionExpiration = async (): Promise<void> => {
  try {
    console.log('Handling session expiration...');
    
    // Clear stored session data
    await clearStoredSessionData();
    
    // Sign out from Supabase
    await supabase.auth.signOut();
    
    console.log('Session cleanup completed');
  } catch (error) {
    console.error('Error handling session expiration:', error);
  }
};

/**
 * Setup automatic session refresh
 */
export const setupSessionRefresh = (): (() => void) => {
  const interval = setInterval(async () => {
    try {
      const { isValid } = await validateAndRefreshSession();
      if (!isValid) {
        console.log('Session validation failed during automatic refresh');
        await handleSessionExpiration();
      }
    } catch (error) {
      console.error('Automatic session refresh failed:', error);
    }
  }, SESSION_CONFIG.REFRESH_THRESHOLD);

  // Return cleanup function
  return () => clearInterval(interval);
};
