// Test utilities for persistent session management
import { useAuthStore } from '@/stores/authStore';
import { 
  storeSessionData, 
  getStoredSessionData, 
  clearStoredSessionData,
  validateAndRefreshSession,
  isStoredSessionValid 
} from '@/utils/sessionUtils';
import { AuthUser } from '@/lib/supabase';

/**
 * Test session persistence functionality
 */
export const testSessionPersistence = async () => {
  console.log('🧪 Testing Session Persistence...');
  
  try {
    // Test 1: Store and retrieve session data
    console.log('📝 Test 1: Store and retrieve session data');
    const mockUser: AuthUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      role: 'student',
      profile: {
        id: 'profile-id',
        name: 'Test Student',
        email: '<EMAIL>',
        grade: '10th',
      }
    };

    await storeSessionData(mockUser);
    const retrievedUser = await getStoredSessionData();
    
    if (retrievedUser && retrievedUser.id === mockUser.id) {
      console.log('✅ Session data stored and retrieved successfully');
    } else {
      console.log('❌ Failed to store/retrieve session data');
    }

    // Test 2: Check session validity
    console.log('📝 Test 2: Check session validity');
    const isValid = await isStoredSessionValid();
    console.log(`✅ Session validity check: ${isValid ? 'Valid' : 'Invalid'}`);

    // Test 3: Clear session data
    console.log('📝 Test 3: Clear session data');
    await clearStoredSessionData();
    const clearedUser = await getStoredSessionData();
    
    if (!clearedUser) {
      console.log('✅ Session data cleared successfully');
    } else {
      console.log('❌ Failed to clear session data');
    }

    console.log('🎉 Session persistence tests completed');
    return true;
  } catch (error) {
    console.error('❌ Session persistence test failed:', error);
    return false;
  }
};

/**
 * Test auth store initialization
 */
export const testAuthStoreInitialization = async () => {
  console.log('🧪 Testing Auth Store Initialization...');
  
  try {
    const authStore = useAuthStore.getState();
    
    // Test initial state
    console.log('📝 Initial auth state:', {
      user: authStore.user,
      isLoading: authStore.isLoading,
      isInitialized: authStore.isInitialized,
      error: authStore.error,
    });

    // Test initialization
    console.log('📝 Testing initializeAuth...');
    await authStore.initializeAuth();
    
    const stateAfterInit = useAuthStore.getState();
    console.log('📝 State after initialization:', {
      user: stateAfterInit.user?.email || 'No user',
      isLoading: stateAfterInit.isLoading,
      isInitialized: stateAfterInit.isInitialized,
      error: stateAfterInit.error,
    });

    if (stateAfterInit.isInitialized) {
      console.log('✅ Auth store initialized successfully');
      return true;
    } else {
      console.log('❌ Auth store initialization failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Auth store initialization test failed:', error);
    return false;
  }
};

/**
 * Test session validation and refresh
 */
export const testSessionValidationAndRefresh = async () => {
  console.log('🧪 Testing Session Validation and Refresh...');
  
  try {
    // Test session validation
    console.log('📝 Testing session validation...');
    const { isValid, user } = await validateAndRefreshSession();
    
    console.log('📝 Session validation result:', {
      isValid,
      userEmail: user?.email || 'No user',
      userRole: user?.role || 'No role',
    });

    if (isValid && user) {
      console.log('✅ Session validation successful');
    } else {
      console.log('ℹ️ No valid session found (expected if not logged in)');
    }

    return true;
  } catch (error) {
    console.error('❌ Session validation test failed:', error);
    return false;
  }
};

/**
 * Test complete authentication flow
 */
export const testCompleteAuthFlow = async (email: string, password: string, role: 'student' | 'teacher') => {
  console.log('🧪 Testing Complete Authentication Flow...');
  
  try {
    const authStore = useAuthStore.getState();
    
    // Test sign in
    console.log('📝 Testing sign in...');
    await authStore.signIn(email, password, role);
    
    let currentState = useAuthStore.getState();
    if (currentState.user) {
      console.log('✅ Sign in successful');
      console.log('📝 User data:', {
        id: currentState.user.id,
        email: currentState.user.email,
        role: currentState.user.role,
        profileName: currentState.user.profile?.name,
      });

      // Test session persistence after sign in
      console.log('📝 Testing session persistence after sign in...');
      const storedUser = await getStoredSessionData();
      if (storedUser && storedUser.id === currentState.user.id) {
        console.log('✅ Session persisted successfully after sign in');
      } else {
        console.log('❌ Session not persisted after sign in');
      }

      // Test session validation
      console.log('📝 Testing session validation...');
      const { isValid, user } = await validateAndRefreshSession();
      if (isValid && user) {
        console.log('✅ Session validation successful');
      } else {
        console.log('❌ Session validation failed');
      }

      // Test sign out
      console.log('📝 Testing sign out...');
      await authStore.signOut();
      
      currentState = useAuthStore.getState();
      if (!currentState.user) {
        console.log('✅ Sign out successful');
        
        // Verify session data is cleared
        const clearedUser = await getStoredSessionData();
        if (!clearedUser) {
          console.log('✅ Session data cleared after sign out');
        } else {
          console.log('❌ Session data not cleared after sign out');
        }
      } else {
        console.log('❌ Sign out failed');
      }

      console.log('🎉 Complete authentication flow test completed');
      return true;
    } else {
      console.log('❌ Sign in failed');
      if (currentState.error) {
        console.log('📝 Error:', currentState.error);
      }
      return false;
    }
  } catch (error) {
    console.error('❌ Complete authentication flow test failed:', error);
    return false;
  }
};

/**
 * Run all session management tests
 */
export const runAllSessionTests = async () => {
  console.log('🚀 Running All Session Management Tests...');
  
  const results = {
    sessionPersistence: await testSessionPersistence(),
    authStoreInitialization: await testAuthStoreInitialization(),
    sessionValidation: await testSessionValidationAndRefresh(),
  };

  console.log('📊 Test Results Summary:', results);
  
  const allPassed = Object.values(results).every(result => result === true);
  console.log(allPassed ? '🎉 All tests passed!' : '⚠️ Some tests failed');
  
  return results;
};

/**
 * Simulate app restart to test session persistence
 */
export const simulateAppRestart = async () => {
  console.log('🔄 Simulating App Restart...');
  
  try {
    // Clear in-memory state (simulate app restart)
    const authStore = useAuthStore.getState();
    
    // Reset store state to initial values
    useAuthStore.setState({
      user: null,
      isLoading: false,
      isInitialized: false,
      error: null,
    });

    console.log('📝 Store state reset, initializing auth...');
    
    // Initialize auth (this should restore from stored session)
    await authStore.initializeAuth();
    
    const restoredState = useAuthStore.getState();
    console.log('📝 State after restart simulation:', {
      user: restoredState.user?.email || 'No user',
      isInitialized: restoredState.isInitialized,
      error: restoredState.error,
    });

    if (restoredState.user) {
      console.log('✅ Session restored successfully after app restart simulation');
      return true;
    } else {
      console.log('ℹ️ No session to restore (expected if not logged in)');
      return true;
    }
  } catch (error) {
    console.error('❌ App restart simulation failed:', error);
    return false;
  }
};
