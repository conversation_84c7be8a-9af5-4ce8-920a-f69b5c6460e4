// Validation utilities for geolocation attendance features

export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

export interface GeofenceValidationData {
  name: string
  center_latitude: number
  center_longitude: number
  radius_meters: number
  allowed_check_in_start?: string
  allowed_check_in_end?: string
  allowed_check_out_start?: string
  allowed_check_out_end?: string
  allowed_days?: number[]
}

export interface AttendanceSettingsValidationData {
  earliest_check_in: string
  latest_check_in: string
  earliest_check_out: string
  latest_check_out: string
  max_location_accuracy_meters: number
  geofence_buffer_meters: number
  max_daily_attempts: number
  lockout_duration_minutes: number
  minimum_work_hours: number
  break_time_minutes: number
}

export function validateCoordinates(lat: number, lng: number): ValidationResult {
  const errors: string[] = []

  if (isNaN(lat) || lat < -90 || lat > 90) {
    errors.push('Latitude must be a valid number between -90 and 90 degrees')
  }

  if (isNaN(lng) || lng < -180 || lng > 180) {
    errors.push('Longitude must be a valid number between -180 and 180 degrees')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export function validateTimeRange(startTime: string, endTime: string, label: string): ValidationResult {
  const errors: string[] = []

  if (!startTime || !endTime) {
    errors.push(`${label} start and end times are required`)
    return { isValid: false, errors }
  }

  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
  
  if (!timeRegex.test(startTime)) {
    errors.push(`${label} start time must be in HH:MM format`)
  }

  if (!timeRegex.test(endTime)) {
    errors.push(`${label} end time must be in HH:MM format`)
  }

  if (timeRegex.test(startTime) && timeRegex.test(endTime)) {
    const start = new Date(`2000-01-01T${startTime}:00`)
    const end = new Date(`2000-01-01T${endTime}:00`)

    if (start >= end) {
      errors.push(`${label} start time must be before end time`)
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export function validateGeofence(data: GeofenceValidationData): ValidationResult {
  const errors: string[] = []

  // Name validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push('Geofence name is required')
  } else if (data.name.trim().length > 100) {
    errors.push('Geofence name must be 100 characters or less')
  }

  // Coordinate validation
  const coordValidation = validateCoordinates(data.center_latitude, data.center_longitude)
  if (!coordValidation.isValid) {
    errors.push(...coordValidation.errors)
  }

  // Radius validation
  if (isNaN(data.radius_meters) || data.radius_meters < 10 || data.radius_meters > 1000) {
    errors.push('Radius must be between 10 and 1000 meters')
  }

  // Time validation
  if (data.allowed_check_in_start && data.allowed_check_in_end) {
    const checkinValidation = validateTimeRange(
      data.allowed_check_in_start,
      data.allowed_check_in_end,
      'Check-in'
    )
    if (!checkinValidation.isValid) {
      errors.push(...checkinValidation.errors)
    }
  }

  if (data.allowed_check_out_start && data.allowed_check_out_end) {
    const checkoutValidation = validateTimeRange(
      data.allowed_check_out_start,
      data.allowed_check_out_end,
      'Check-out'
    )
    if (!checkoutValidation.isValid) {
      errors.push(...checkoutValidation.errors)
    }
  }

  // Days validation
  if (data.allowed_days && data.allowed_days.length === 0) {
    errors.push('At least one day must be selected')
  }

  if (data.allowed_days && data.allowed_days.some(day => day < 0 || day > 6)) {
    errors.push('Invalid day selected (must be 0-6)')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export function validateAttendanceSettings(data: AttendanceSettingsValidationData): ValidationResult {
  const errors: string[] = []

  // Time range validations
  const checkinValidation = validateTimeRange(
    data.earliest_check_in,
    data.latest_check_in,
    'Check-in window'
  )
  if (!checkinValidation.isValid) {
    errors.push(...checkinValidation.errors)
  }

  const checkoutValidation = validateTimeRange(
    data.earliest_check_out,
    data.latest_check_out,
    'Check-out window'
  )
  if (!checkoutValidation.isValid) {
    errors.push(...checkoutValidation.errors)
  }

  // Location accuracy validation
  if (isNaN(data.max_location_accuracy_meters) || 
      data.max_location_accuracy_meters < 1 || 
      data.max_location_accuracy_meters > 500) {
    errors.push('Location accuracy must be between 1 and 500 meters')
  }

  // Geofence buffer validation
  if (isNaN(data.geofence_buffer_meters) || 
      data.geofence_buffer_meters < 0 || 
      data.geofence_buffer_meters > 1000) {
    errors.push('Geofence buffer must be between 0 and 1000 meters')
  }

  // Daily attempts validation
  if (isNaN(data.max_daily_attempts) || 
      data.max_daily_attempts < 1 || 
      data.max_daily_attempts > 50) {
    errors.push('Max daily attempts must be between 1 and 50')
  }

  // Lockout duration validation
  if (isNaN(data.lockout_duration_minutes) || 
      data.lockout_duration_minutes < 1 || 
      data.lockout_duration_minutes > 1440) {
    errors.push('Lockout duration must be between 1 and 1440 minutes (24 hours)')
  }

  // Work hours validation
  if (isNaN(data.minimum_work_hours) || 
      data.minimum_work_hours < 0 || 
      data.minimum_work_hours > 24) {
    errors.push('Minimum work hours must be between 0 and 24 hours')
  }

  // Break time validation
  if (isNaN(data.break_time_minutes) || 
      data.break_time_minutes < 0 || 
      data.break_time_minutes > 480) {
    errors.push('Break time must be between 0 and 480 minutes (8 hours)')
  }

  // Logical validations
  if (data.minimum_work_hours > 0 && data.break_time_minutes >= (data.minimum_work_hours * 60)) {
    errors.push('Break time cannot be equal to or greater than minimum work hours')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export function formatValidationErrors(errors: string[]): string {
  if (errors.length === 0) return ''
  if (errors.length === 1) return errors[0]
  
  return `Multiple validation errors:\n• ${errors.join('\n• ')}`
}
