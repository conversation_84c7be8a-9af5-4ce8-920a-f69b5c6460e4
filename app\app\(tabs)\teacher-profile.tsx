import TeacherAttendanceModal from '@/components/TeacherAttendanceModal';
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Alert, Modal, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function TeacherProfile() {
  const router = useRouter();
  const { user, signOut, changePassword } = useAuthStore();
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showAttendanceModal, setShowAttendanceModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const currentTeacher = user?.role === 'teacher' ? user.profile : null;

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await signOut();
            router.replace('/(auth)/sign-in');
          }
        },
      ]
    );
  };

  const handleChangePassword = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      Alert.alert('Error', 'Please fill in all password fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'New passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      Alert.alert('Error', 'New password must be at least 6 characters long');
      return;
    }

    try {
      await changePassword(currentPassword, newPassword);
      Alert.alert('Success', 'Password changed successfully');
      setShowPasswordModal(false);
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleMarkAttendance = () => {
    setShowAttendanceModal(true);
  };

  return (
    <ScrollView className="flex-1 bg-light-background dark:bg-dark-background">
      {/* Header */}
      <View className="bg-secondary-500 pt-12 pb-6 px-6">
        <View className="flex-row justify-between items-center">
          <View>
            <Text className="text-white text-2xl font-rubik-bold">
              My Profile
            </Text>
            <Text className="text-secondary-100 font-rubik">
              Teacher Dashboard
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleSignOut}
            className="bg-white/20 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-rubik-medium">Sign Out</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View className="px-6 py-6">
        {/* Personal Information */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Personal Information
          </Text>

          <View className="flex flex-col gap-3">
            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Name</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentTeacher?.name || 'Loading...'}
              </Text>
            </View>

            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Email</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentTeacher?.email || 'Loading...'}
              </Text>
            </View>

            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Teacher ID</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentTeacher?.teacher_id || 'Not assigned'}
              </Text>
            </View>

            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Subject</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentTeacher?.subject || 'Not assigned'}
              </Text>
            </View>

            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Department</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentTeacher?.department || 'Not assigned'}
              </Text>
            </View>

            {currentTeacher?.phone && (
              <View>
                <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Phone</Text>
                <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                  {currentTeacher.phone}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Quick Actions */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 mb-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Quick Actions
          </Text>

          <TouchableOpacity
            onPress={handleMarkAttendance}
            className="bg-secondary-50 dark:bg-secondary-900/20 rounded-lg p-4 mb-3"
          >
            <Text className="font-rubik-medium text-secondary-700 dark:text-secondary-300">
              Mark My Attendance
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => setShowPasswordModal(true)}
            className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4"
          >
            <Text className="font-rubik-medium text-primary-700 dark:text-primary-300">
              Change Password
            </Text>
          </TouchableOpacity>
        </View>

        {/* Account Information */}
        <View className="bg-white dark:bg-neutral-800 rounded-xl p-6 shadow-sm">
          <Text className="text-xl font-rubik-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Account Information
          </Text>

          <View className="flex flex-col gap-3">
            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Account Status</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentTeacher?.is_active ? 'Active' : 'Inactive'}
              </Text>
            </View>

            <View>
              <Text className="text-sm font-rubik-medium text-neutral-500 dark:text-neutral-400">Member Since</Text>
              <Text className="text-lg font-rubik text-neutral-900 dark:text-neutral-100">
                {currentTeacher?.created_at
                  ? new Date(currentTeacher.created_at).toLocaleDateString()
                  : 'Unknown'
                }
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Change Password Modal */}
      <Modal
        visible={showPasswordModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowPasswordModal(false)}
      >
        <View className="flex-1 bg-light-background dark:bg-dark-background">
          <View className="bg-secondary-500 pt-12 pb-6 px-6">
            <View className="flex-row justify-between items-center">
              <Text className="text-white text-xl font-rubik-bold">Change Password</Text>
              <TouchableOpacity
                onPress={() => setShowPasswordModal(false)}
                className="bg-white/20 px-4 py-2 rounded-lg"
              >
                <Text className="text-white font-rubik-medium">Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View className="flex-1 px-6 py-6">
            <View className="mb-6">
              <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                Current Password
              </Text>
              <TextInput
                value={currentPassword}
                onChangeText={setCurrentPassword}
                placeholder="Enter current password"
                secureTextEntry
                className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
              />
            </View>

            <View className="mb-6">
              <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                New Password
              </Text>
              <TextInput
                value={newPassword}
                onChangeText={setNewPassword}
                placeholder="Enter new password"
                secureTextEntry
                className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
              />
            </View>

            <View className="mb-6">
              <Text className="text-sm font-rubik-medium text-neutral-700 dark:text-neutral-300 mb-2">
                Confirm New Password
              </Text>
              <TextInput
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder="Confirm new password"
                secureTextEntry
                className="w-full px-4 py-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-neutral-900 dark:text-neutral-100 font-rubik"
              />
            </View>

            <TouchableOpacity
              onPress={handleChangePassword}
              className="w-full py-4 bg-secondary-500 rounded-xl"
            >
              <Text className="text-white text-center font-rubik-semibold text-lg">
                Change Password
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Teacher Attendance Modal */}
      <TeacherAttendanceModal
        visible={showAttendanceModal}
        onClose={() => setShowAttendanceModal(false)}
      />
    </ScrollView>
  );
}
