'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { validateAttendanceSettings, formatValidationErrors } from '@/lib/validation'
import {
  Shield,
  Clock,
  MapPin,
  Bell,
  Camera,
  Settings,
  Save,
  AlertCircle,
  CheckCircle,
  Lock,
  Timer,
  Target
} from 'lucide-react'

interface AttendanceSettings {
  earliest_check_in: string
  latest_check_in: string
  earliest_check_out: string
  latest_check_out: string
  require_location: boolean
  max_location_accuracy_meters: number
  geofence_buffer_meters: number
  max_daily_attempts: number
  lockout_duration_minutes: number
  require_photo_verification: boolean
  allow_manual_override: boolean
  notify_admin_on_suspicious: boolean
  notify_teacher_on_location_fail: boolean
  minimum_work_hours: number
  break_time_minutes: number
}

interface AttendanceSecuritySettingsProps {
  onSave?: () => void
}

export default function AttendanceSecuritySettings({ onSave }: AttendanceSecuritySettingsProps) {
  const [settings, setSettings] = useState<AttendanceSettings>({
    earliest_check_in: '06:00',
    latest_check_in: '10:00',
    earliest_check_out: '14:00',
    latest_check_out: '20:00',
    require_location: true,
    max_location_accuracy_meters: 50,
    geofence_buffer_meters: 100,
    max_daily_attempts: 5,
    lockout_duration_minutes: 30,
    require_photo_verification: false,
    allow_manual_override: true,
    notify_admin_on_suspicious: true,
    notify_teacher_on_location_fail: true,
    minimum_work_hours: 8.0,
    break_time_minutes: 60
  })

  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [message, setMessage] = useState('')

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/attendance-settings')
      const data = await response.json()

      if (data.success) {
        const fetchedSettings = data.data
        setSettings({
          ...fetchedSettings,
          earliest_check_in: fetchedSettings.earliest_check_in?.substring(0, 5) || '06:00',
          latest_check_in: fetchedSettings.latest_check_in?.substring(0, 5) || '10:00',
          earliest_check_out: fetchedSettings.earliest_check_out?.substring(0, 5) || '14:00',
          latest_check_out: fetchedSettings.latest_check_out?.substring(0, 5) || '20:00'
        })
      } else {
        setError(data.error || 'Failed to fetch settings')
      }
    } catch (err) {
      setError('Failed to fetch settings')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof AttendanceSettings, value: any) => {
    setSettings({
      ...settings,
      [field]: value
    })
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      setError('')

      // Validate the settings
      const validation = validateAttendanceSettings({
        earliest_check_in: settings.earliest_check_in,
        latest_check_in: settings.latest_check_in,
        earliest_check_out: settings.earliest_check_out,
        latest_check_out: settings.latest_check_out,
        max_location_accuracy_meters: settings.max_location_accuracy_meters,
        geofence_buffer_meters: settings.geofence_buffer_meters,
        max_daily_attempts: settings.max_daily_attempts,
        lockout_duration_minutes: settings.lockout_duration_minutes,
        minimum_work_hours: settings.minimum_work_hours,
        break_time_minutes: settings.break_time_minutes
      })

      if (!validation.isValid) {
        setError(formatValidationErrors(validation.errors))
        return
      }

      const payload = {
        ...settings,
        earliest_check_in: settings.earliest_check_in + ':00',
        latest_check_in: settings.latest_check_in + ':00',
        earliest_check_out: settings.earliest_check_out + ':00',
        latest_check_out: settings.latest_check_out + ':00'
      }

      const response = await fetch('/api/attendance-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (data.success) {
        setMessage('Attendance settings updated successfully!')
        if (onSave) onSave()
        setTimeout(() => setMessage(''), 3000)
      } else {
        setError(data.error || 'Failed to update settings')
      }
    } catch (err) {
      setError('Failed to update settings')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-600 border-t-transparent mx-auto mb-2" />
            <p className="text-gray-600">Loading settings...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Messages */}
      {message && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{message}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-600" />
            Teacher Attendance Security Settings
          </CardTitle>
          <CardDescription>
            Configure security policies and restrictions for teacher attendance tracking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Time Restrictions */}
          <div>
            <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
              <Clock className="h-5 w-5 text-green-600" />
              Time Restrictions
            </h3>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label className="text-sm font-medium mb-2 block">Check-in Window</Label>
                <div className="grid gap-2 md:grid-cols-2">
                  <div>
                    <Label htmlFor="earliest-checkin" className="text-xs text-gray-600">Earliest</Label>
                    <Input
                      id="earliest-checkin"
                      type="time"
                      value={settings.earliest_check_in}
                      onChange={(e) => handleInputChange('earliest_check_in', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="latest-checkin" className="text-xs text-gray-600">Latest</Label>
                    <Input
                      id="latest-checkin"
                      type="time"
                      value={settings.latest_check_in}
                      onChange={(e) => handleInputChange('latest_check_in', e.target.value)}
                    />
                  </div>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium mb-2 block">Check-out Window</Label>
                <div className="grid gap-2 md:grid-cols-2">
                  <div>
                    <Label htmlFor="earliest-checkout" className="text-xs text-gray-600">Earliest</Label>
                    <Input
                      id="earliest-checkout"
                      type="time"
                      value={settings.earliest_check_out}
                      onChange={(e) => handleInputChange('earliest_check_out', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="latest-checkout" className="text-xs text-gray-600">Latest</Label>
                    <Input
                      id="latest-checkout"
                      type="time"
                      value={settings.latest_check_out}
                      onChange={(e) => handleInputChange('latest_check_out', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Location Settings */}
          <div>
            <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
              <MapPin className="h-5 w-5 text-blue-600" />
              Location Settings
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Require Location Verification</Label>
                  <p className="text-xs text-gray-600">Teachers must be within geofenced areas to mark attendance</p>
                </div>
                <Switch
                  checked={settings.require_location}
                  onCheckedChange={(checked) => handleInputChange('require_location', checked)}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="location-accuracy" className="text-sm font-medium">
                    Required GPS Accuracy (meters)
                  </Label>
                  <Input
                    id="location-accuracy"
                    type="number"
                    min="1"
                    max="500"
                    value={settings.max_location_accuracy_meters}
                    onChange={(e) => handleInputChange('max_location_accuracy_meters', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-gray-600 mt-1">Lower values require more precise GPS</p>
                </div>
                <div>
                  <Label htmlFor="geofence-buffer" className="text-sm font-medium">
                    Geofence Buffer (meters)
                  </Label>
                  <Input
                    id="geofence-buffer"
                    type="number"
                    min="0"
                    max="1000"
                    value={settings.geofence_buffer_meters}
                    onChange={(e) => handleInputChange('geofence_buffer_meters', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-gray-600 mt-1">Additional buffer around geofence boundaries</p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Security Settings */}
          <div>
            <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
              <Lock className="h-5 w-5 text-red-600" />
              Security Settings
            </h3>
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="max-attempts" className="text-sm font-medium">
                    Max Daily Attempts
                  </Label>
                  <Input
                    id="max-attempts"
                    type="number"
                    min="1"
                    max="50"
                    value={settings.max_daily_attempts}
                    onChange={(e) => handleInputChange('max_daily_attempts', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-gray-600 mt-1">Maximum failed attendance attempts per day</p>
                </div>
                <div>
                  <Label htmlFor="lockout-duration" className="text-sm font-medium">
                    Lockout Duration (minutes)
                  </Label>
                  <Input
                    id="lockout-duration"
                    type="number"
                    min="1"
                    max="1440"
                    value={settings.lockout_duration_minutes}
                    onChange={(e) => handleInputChange('lockout_duration_minutes', parseInt(e.target.value))}
                  />
                  <p className="text-xs text-gray-600 mt-1">How long to lock out after max attempts</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Require Photo Verification</Label>
                    <p className="text-xs text-gray-600">Teachers must take a photo when marking attendance</p>
                  </div>
                  <Switch
                    checked={settings.require_photo_verification}
                    onCheckedChange={(checked) => handleInputChange('require_photo_verification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Allow Manual Override</Label>
                    <p className="text-xs text-gray-600">Admins can manually mark attendance for teachers</p>
                  </div>
                  <Switch
                    checked={settings.allow_manual_override}
                    onCheckedChange={(checked) => handleInputChange('allow_manual_override', checked)}
                  />
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Notification Settings */}
          <div>
            <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
              <Bell className="h-5 w-5 text-yellow-600" />
              Notification Settings
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Notify Admin on Suspicious Activity</Label>
                  <p className="text-xs text-gray-600">Send alerts when unusual attendance patterns are detected</p>
                </div>
                <Switch
                  checked={settings.notify_admin_on_suspicious}
                  onCheckedChange={(checked) => handleInputChange('notify_admin_on_suspicious', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Notify Teacher on Location Failure</Label>
                  <p className="text-xs text-gray-600">Inform teachers when location verification fails</p>
                </div>
                <Switch
                  checked={settings.notify_teacher_on_location_fail}
                  onCheckedChange={(checked) => handleInputChange('notify_teacher_on_location_fail', checked)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Work Hours Settings */}
          <div>
            <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
              <Timer className="h-5 w-5 text-purple-600" />
              Work Hours Configuration
            </h3>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="min-work-hours" className="text-sm font-medium">
                  Minimum Work Hours
                </Label>
                <Input
                  id="min-work-hours"
                  type="number"
                  min="0"
                  max="24"
                  step="0.5"
                  value={settings.minimum_work_hours}
                  onChange={(e) => handleInputChange('minimum_work_hours', parseFloat(e.target.value))}
                />
                <p className="text-xs text-gray-600 mt-1">Required daily work hours</p>
              </div>
              <div>
                <Label htmlFor="break-time" className="text-sm font-medium">
                  Break Time (minutes)
                </Label>
                <Input
                  id="break-time"
                  type="number"
                  min="0"
                  max="480"
                  value={settings.break_time_minutes}
                  onChange={(e) => handleInputChange('break_time_minutes', parseInt(e.target.value))}
                />
                <p className="text-xs text-gray-600 mt-1">Daily break time allowance</p>
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4">
            <Button onClick={saveSettings} disabled={saving}>
              {saving ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Saving Settings...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
