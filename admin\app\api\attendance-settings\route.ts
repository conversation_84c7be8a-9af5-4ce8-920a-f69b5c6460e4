import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return NextResponse.json({ error: 'School not found' }, { status: 404 })
    }

    // Get attendance settings for the school
    const { data: settings, error } = await supabase
      .from('teacher_attendance_settings')
      .select('*')
      .eq('school_id', schoolAdmin.school_id)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      console.error('Error fetching attendance settings:', error)
      return NextResponse.json({ error: 'Failed to fetch attendance settings' }, { status: 500 })
    }

    // If no settings exist, create default settings
    if (!settings) {
      const { data: newSettings, error: createError } = await supabase
        .from('teacher_attendance_settings')
        .insert([{
          school_id: schoolAdmin.school_id,
          updated_by: user.id
        }])
        .select()
        .single()

      if (createError) {
        console.error('Error creating default attendance settings:', createError)
        return NextResponse.json({ error: 'Failed to create default settings' }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        data: newSettings
      })
    }

    return NextResponse.json({
      success: true,
      data: settings
    })

  } catch (error) {
    console.error('Error in attendance-settings GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return NextResponse.json({ error: 'School not found' }, { status: 404 })
    }

    const body = await request.json()
    const {
      earliest_check_in,
      latest_check_in,
      earliest_check_out,
      latest_check_out,
      require_location,
      max_location_accuracy_meters,
      geofence_buffer_meters,
      max_daily_attempts,
      lockout_duration_minutes,
      require_photo_verification,
      allow_manual_override,
      notify_admin_on_suspicious,
      notify_teacher_on_location_fail,
      minimum_work_hours,
      break_time_minutes
    } = body

    // Validation
    if (max_location_accuracy_meters !== undefined && (max_location_accuracy_meters < 1 || max_location_accuracy_meters > 500)) {
      return NextResponse.json({
        error: 'Invalid location accuracy. Must be between 1 and 500 meters'
      }, { status: 400 })
    }

    if (geofence_buffer_meters !== undefined && (geofence_buffer_meters < 0 || geofence_buffer_meters > 1000)) {
      return NextResponse.json({
        error: 'Invalid geofence buffer. Must be between 0 and 1000 meters'
      }, { status: 400 })
    }

    if (max_daily_attempts !== undefined && (max_daily_attempts < 1 || max_daily_attempts > 50)) {
      return NextResponse.json({
        error: 'Invalid max daily attempts. Must be between 1 and 50'
      }, { status: 400 })
    }

    if (lockout_duration_minutes !== undefined && (lockout_duration_minutes < 1 || lockout_duration_minutes > 1440)) {
      return NextResponse.json({
        error: 'Invalid lockout duration. Must be between 1 and 1440 minutes (24 hours)'
      }, { status: 400 })
    }

    if (minimum_work_hours !== undefined && (minimum_work_hours < 0 || minimum_work_hours > 24)) {
      return NextResponse.json({
        error: 'Invalid minimum work hours. Must be between 0 and 24 hours'
      }, { status: 400 })
    }

    if (break_time_minutes !== undefined && (break_time_minutes < 0 || break_time_minutes > 480)) {
      return NextResponse.json({
        error: 'Invalid break time. Must be between 0 and 480 minutes (8 hours)'
      }, { status: 400 })
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString(),
      updated_by: user.id
    }

    if (earliest_check_in !== undefined) updateData.earliest_check_in = earliest_check_in
    if (latest_check_in !== undefined) updateData.latest_check_in = latest_check_in
    if (earliest_check_out !== undefined) updateData.earliest_check_out = earliest_check_out
    if (latest_check_out !== undefined) updateData.latest_check_out = latest_check_out
    if (require_location !== undefined) updateData.require_location = require_location
    if (max_location_accuracy_meters !== undefined) updateData.max_location_accuracy_meters = max_location_accuracy_meters
    if (geofence_buffer_meters !== undefined) updateData.geofence_buffer_meters = geofence_buffer_meters
    if (max_daily_attempts !== undefined) updateData.max_daily_attempts = max_daily_attempts
    if (lockout_duration_minutes !== undefined) updateData.lockout_duration_minutes = lockout_duration_minutes
    if (require_photo_verification !== undefined) updateData.require_photo_verification = require_photo_verification
    if (allow_manual_override !== undefined) updateData.allow_manual_override = allow_manual_override
    if (notify_admin_on_suspicious !== undefined) updateData.notify_admin_on_suspicious = notify_admin_on_suspicious
    if (notify_teacher_on_location_fail !== undefined) updateData.notify_teacher_on_location_fail = notify_teacher_on_location_fail
    if (minimum_work_hours !== undefined) updateData.minimum_work_hours = minimum_work_hours
    if (break_time_minutes !== undefined) updateData.break_time_minutes = break_time_minutes

    // Update or insert settings
    const { data: settings, error } = await supabase
      .from('teacher_attendance_settings')
      .upsert({
        school_id: schoolAdmin.school_id,
        ...updateData
      })
      .select()
      .single()

    if (error) {
      console.error('Error updating attendance settings:', error)
      return NextResponse.json({ error: 'Failed to update attendance settings' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: settings,
      message: 'Attendance settings updated successfully'
    })

  } catch (error) {
    console.error('Error in attendance-settings PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
