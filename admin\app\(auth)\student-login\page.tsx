import React from 'react'
import { StudentLoginForm } from './student-login-form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GraduationCap } from 'lucide-react'

export default function StudentLoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-blue-600 p-3 rounded-full">
              <GraduationCap className="h-8 w-8 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold">Student Portal</CardTitle>
          <CardDescription>
            Sign in to access your student dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <StudentLoginForm />
          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              Need help? Contact your school administrator
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
