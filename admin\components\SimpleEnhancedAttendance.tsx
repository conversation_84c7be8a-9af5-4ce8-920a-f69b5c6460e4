'use client'

import React, { useState, useEffect } from 'react'
import { useAttendanceStore } from '@/stores/attendanceStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { GraduationCap, Users } from 'lucide-react'

function SimpleEnhancedAttendance() {
  const {
    grades = [],
    selectedGrade,
    studentsInGrade = [],
    markingMode = 'individual',
    loading = false,
    error,
    fetchGrades,
    setSelectedGrade,
    setMarkingMode,
  } = useAttendanceStore()

  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])

  useEffect(() => {
    if (fetchGrades && typeof fetchGrades === 'function') {
      fetchGrades().catch(console.error)
    }
  }, [fetchGrades])

  const handleGradeChange = (grade: string) => {
    if (setSelectedGrade && typeof setSelectedGrade === 'function') {
      setSelectedGrade(grade)
    }
  }

  const handleModeChange = (checked: boolean) => {
    if (setMarkingMode && typeof setMarkingMode === 'function') {
      setMarkingMode(checked ? 'bulk' : 'individual')
    }
  }

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-red-600 text-sm">{error}</div>
          </CardContent>
        </Card>
      )}

      {/* Grade Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GraduationCap className="h-5 w-5" />
            Enhanced Attendance Marking
          </CardTitle>
          <CardDescription>
            Mark attendance by grade with bulk or individual options
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Grade Selection */}
            <div>
              <label className="text-sm font-medium mb-2 block">Select Grade</label>
              <Select value={selectedGrade || ''} onValueChange={handleGradeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a grade" />
                </SelectTrigger>
                <SelectContent>
                  {grades.map(grade => (
                    <SelectItem key={grade} value={grade}>
                      Grade {grade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date Selection */}
            <div>
              <label className="text-sm font-medium mb-2 block">Date</label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>

            {/* Marking Mode Toggle */}
            <div>
              <label className="text-sm font-medium mb-2 block">Marking Mode</label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="marking-mode"
                  checked={markingMode === 'bulk'}
                  onCheckedChange={handleModeChange}
                />
                <Label htmlFor="marking-mode" className="text-sm">
                  {markingMode === 'bulk' ? 'Bulk Mode' : 'Individual Mode'}
                </Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Students Display */}
      {selectedGrade && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Grade {selectedGrade} Students ({studentsInGrade.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">Loading students...</div>
            ) : studentsInGrade.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No students found in Grade {selectedGrade}
              </div>
            ) : (
              <div className="space-y-2">
                {studentsInGrade.map(student => (
                  <div key={student.id} className="p-3 border rounded-lg">
                    <div className="font-medium">{student.name}</div>
                    <div className="text-sm text-muted-foreground">
                      ID: {student.student_id} | Class: {student.class_name} {student.section && `(${student.section})`}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

SimpleEnhancedAttendance.displayName = 'SimpleEnhancedAttendance'

export default SimpleEnhancedAttendance
