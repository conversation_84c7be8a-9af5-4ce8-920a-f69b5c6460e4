// Application configuration for caching and session management

export const APP_CONFIG = {
  // Session Management
  SESSION: {
    CHECK_INTERVAL: 15 * 60 * 1000, // 15 minutes
    TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
    BACKGROUND_CHECK_DELAY: 5000, // 5 seconds
    THROTTLE_DELAY: 30000, // 30 seconds
    DEBOUNCE_DELAY: 5000, // 5 seconds
  },

  // Data Caching
  CACHE: {
    STUDENT_DATA_TTL: 10 * 60 * 1000, // 10 minutes
    ATTENDANCE_DATA_TTL: 5 * 60 * 1000, // 5 minutes
    TEACHER_DATA_TTL: 10 * 60 * 1000, // 10 minutes
    CLASSES_DATA_TTL: 15 * 60 * 1000, // 15 minutes
  },

  // Network & Performance
  NETWORK: {
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 2000, // 2 seconds
    TIMEOUT: 10000, // 10 seconds
    OFFLINE_RETRY_DELAY: 30000, // 30 seconds
  },

  // UI & UX
  UI: {
    LOADING_DELAY: 300, // Show loading after 300ms
    ERROR_DISPLAY_DURATION: 5000, // 5 seconds
    SUCCESS_DISPLAY_DURATION: 3000, // 3 seconds
  },

  // Development
  DEV: {
    ENABLE_LOGGING: __DEV__,
    ENABLE_PERFORMANCE_MONITORING: __DEV__,
    CACHE_DEBUG: __DEV__,
  },
} as const;

// Helper functions for configuration
export const getSessionConfig = () => APP_CONFIG.SESSION;
export const getCacheConfig = () => APP_CONFIG.CACHE;
export const getNetworkConfig = () => APP_CONFIG.NETWORK;
export const getUIConfig = () => APP_CONFIG.UI;
export const getDevConfig = () => APP_CONFIG.DEV;

// Environment-specific overrides
export const getEnvironmentConfig = () => {
  if (__DEV__) {
    return {
      ...APP_CONFIG,
      SESSION: {
        ...APP_CONFIG.SESSION,
        CHECK_INTERVAL: 30 * 60 * 1000, // 30 minutes in dev (less frequent)
      },
      CACHE: {
        ...APP_CONFIG.CACHE,
        STUDENT_DATA_TTL: 30 * 60 * 1000, // 30 minutes in dev
        ATTENDANCE_DATA_TTL: 15 * 60 * 1000, // 15 minutes in dev
      },
    };
  }
  return APP_CONFIG;
};

export default APP_CONFIG;
