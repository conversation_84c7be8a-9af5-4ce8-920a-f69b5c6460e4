'use client'

import React, { useState, useEffect } from 'react'
import { useAttendanceStore } from '@/stores/attendanceStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Calendar, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Shield,
  Download,
  Filter
} from 'lucide-react'

const COLORS = {
  present: '#22c55e',
  absent: '#ef4444',
  late: '#f59e0b',
  excused: '#3b82f6'
}

interface AttendanceReport {
  date: string
  present: number
  absent: number
  late: number
  excused: number
  total: number
  rate: number
}

export default function AttendanceDashboard() {
  const {
    classes,
    attendanceStats,
    loading,
    error,
    fetchClasses,
    fetchAttendanceStats,
    clearError
  } = useAttendanceStore()

  const [selectedClass, setSelectedClass] = useState<string>('all')
  const [dateRange, setDateRange] = useState<string>('30')
  const [reportData, setReportData] = useState<AttendanceReport[]>([])

  useEffect(() => {
    fetchClasses()
  }, [fetchClasses])

  useEffect(() => {
    const endDate = new Date().toISOString().split('T')[0]
    const startDate = new Date(Date.now() - parseInt(dateRange) * 24 * 60 * 60 * 1000)
      .toISOString().split('T')[0]
    
    const classId = selectedClass === 'all' ? undefined : selectedClass
    fetchAttendanceStats(classId, startDate, endDate)
    
    // Generate sample report data for charts
    generateReportData(startDate, endDate)
  }, [selectedClass, dateRange, fetchAttendanceStats])

  const generateReportData = (startDate: string, endDate: string) => {
    // This would typically come from the backend
    // For now, generating sample data
    const data: AttendanceReport[] = []
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      const total = Math.floor(Math.random() * 100) + 50
      const present = Math.floor(total * (0.85 + Math.random() * 0.1))
      const absent = Math.floor((total - present) * 0.6)
      const late = Math.floor((total - present - absent) * 0.7)
      const excused = total - present - absent - late
      
      data.push({
        date: dateStr,
        present,
        absent,
        late,
        excused,
        total,
        rate: (present / total) * 100
      })
    }
    
    setReportData(data)
  }

  const getAttendanceOverview = () => {
    if (!attendanceStats) return null

    const total = attendanceStats.presentCount + attendanceStats.absentCount + 
                 attendanceStats.lateCount + attendanceStats.excusedCount

    return [
      {
        name: 'Present',
        value: attendanceStats.presentCount,
        percentage: total > 0 ? (attendanceStats.presentCount / total) * 100 : 0,
        color: COLORS.present,
        icon: CheckCircle
      },
      {
        name: 'Absent',
        value: attendanceStats.absentCount,
        percentage: total > 0 ? (attendanceStats.absentCount / total) * 100 : 0,
        color: COLORS.absent,
        icon: XCircle
      },
      {
        name: 'Late',
        value: attendanceStats.lateCount,
        percentage: total > 0 ? (attendanceStats.lateCount / total) * 100 : 0,
        color: COLORS.late,
        icon: Clock
      },
      {
        name: 'Excused',
        value: attendanceStats.excusedCount,
        percentage: total > 0 ? (attendanceStats.excusedCount / total) * 100 : 0,
        color: COLORS.excused,
        icon: Shield
      }
    ]
  }

  const pieData = getAttendanceOverview()?.map(item => ({
    name: item.name,
    value: item.value,
    color: item.color
  })) || []

  const handleExportReport = () => {
    // This would generate and download a CSV/PDF report
    console.log('Exporting report for:', { selectedClass, dateRange })
    // Implementation would go here
  }

  if (loading && !attendanceStats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading attendance data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-destructive/15 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-destructive">{error}</p>
            <Button variant="ghost" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </div>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Attendance Analytics
          </CardTitle>
          <CardDescription>
            View attendance statistics and trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Class:</label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.class_name} - Grade {cls.grade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Period:</label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 3 months</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button variant="outline" onClick={handleExportReport} className="ml-auto">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Overview Cards */}
      {attendanceStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {getAttendanceOverview()?.map((item) => {
            const Icon = item.icon
            return (
              <Card key={item.name}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">{item.name}</p>
                      <p className="text-2xl font-bold">{item.value}</p>
                      <p className="text-xs text-muted-foreground">
                        {item.percentage.toFixed(1)}% of total
                      </p>
                    </div>
                    <div className="h-12 w-12 rounded-full flex items-center justify-center" 
                         style={{ backgroundColor: `${item.color}20` }}>
                      <Icon className="h-6 w-6" style={{ color: item.color }} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Attendance Distribution Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Attendance Distribution</CardTitle>
            <CardDescription>
              Overall attendance breakdown for selected period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percentage }) => `${name}: ${percentage?.toFixed(1)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Trend Line Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Attendance Trend</CardTitle>
            <CardDescription>
              Daily attendance rate over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={reportData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={(value) => new Date(value).toLocaleDateString()}
                  />
                  <YAxis domain={[0, 100]} />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleDateString()}
                    formatter={(value: number) => [`${value.toFixed(1)}%`, 'Attendance Rate']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="rate" 
                    stroke="#22c55e" 
                    strokeWidth={2}
                    dot={{ fill: '#22c55e' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Daily Breakdown Bar Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Daily Attendance Breakdown</CardTitle>
          <CardDescription>
            Detailed daily attendance statistics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={reportData.slice(-14)}> {/* Show last 14 days */}
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <Bar dataKey="present" stackId="a" fill={COLORS.present} name="Present" />
                <Bar dataKey="late" stackId="a" fill={COLORS.late} name="Late" />
                <Bar dataKey="excused" stackId="a" fill={COLORS.excused} name="Excused" />
                <Bar dataKey="absent" stackId="a" fill={COLORS.absent} name="Absent" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      {attendanceStats && (
        <Card>
          <CardHeader>
            <CardTitle>Summary Statistics</CardTitle>
            <CardDescription>
              Key attendance metrics for the selected period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {attendanceStats.attendanceRate.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">Overall Attendance Rate</div>
                <div className="flex items-center justify-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-xs text-green-600">Good performance</span>
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold">
                  {attendanceStats.totalStudents}
                </div>
                <div className="text-sm text-muted-foreground">Total Students</div>
                <Badge variant="outline" className="mt-2">
                  Active enrollment
                </Badge>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">
                  {attendanceStats.absentCount}
                </div>
                <div className="text-sm text-muted-foreground">Total Absences</div>
                <div className="flex items-center justify-center mt-2">
                  <XCircle className="h-4 w-4 text-red-600 mr-1" />
                  <span className="text-xs text-red-600">Needs attention</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
