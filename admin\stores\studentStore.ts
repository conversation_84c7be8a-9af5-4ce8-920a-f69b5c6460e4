import { create } from 'zustand'
import { Student, StudentFormData, updateStudent as updateStudentLib } from '@/lib/supabase'
import { validateStudentData, handleError, rateLimiter } from '@/lib/security'
import { createClient } from '@/utils/supabase/client'
import { enrollStudentInClass } from '@/app/(admin)/students/class-actions'

interface StudentRegistrationResult {
  success: boolean
  studentId: string
  temporaryPassword: string
  message: string
}

interface StudentStore {
  students: Student[]
  loading: boolean
  error: string | null
  fetchStudents: () => Promise<void>
  addStudent: (student: StudentFormData) => Promise<StudentRegistrationResult | null>
  updateStudent: (id: string, updates: Partial<StudentFormData>) => Promise<void>
  deleteStudent: (id: string) => Promise<void>
  clearError: () => void
}

// Rate limiting identifier (in production, use user ID or IP)
const getRateLimitId = () => 'admin-user' // Simplified for demo

export const useStudentStore = create<StudentStore>((set, get) => {
  const supabase = createClient()

  return {
    students: [],
    loading: false,
    error: null,

    fetchStudents: async () => {
      set({ loading: true, error: null })
      try {
        // Use server action for fetching students
        const { getStudents } = await import('@/app/(admin)/students/actions')
        const students = await getStudents()

        set({ students: students || [], loading: false })
      } catch (error) {
        set({
          error: handleError(error),
          loading: false
        })
      }
    },

  addStudent: async (student: StudentFormData) => {
    set({ loading: true, error: null })
    try {
      // Rate limiting check
      if (!rateLimiter.isAllowed(getRateLimitId())) {
        throw new Error('Too many requests. Please wait before trying again.')
      }

      // Validate and sanitize input data
      const validation = validateStudentData(student)
      if (!validation.isValid) {
        const errorMessage = Object.values(validation.errors)[0]
        throw new Error(errorMessage)
      }

      // Use server action for student registration
      const formData = new FormData()
      Object.entries(validation.data).forEach(([key, value]) => {
        formData.append(key, value)
      })

      // Import the server action
      const { registerStudent } = await import('@/app/(admin)/students/actions')
      const result = await registerStudent(formData)

      if (result.error) {
        throw new Error(result.error)
      }

      // Refresh the students list
      await get().fetchStudents()
      set({ loading: false })

      // Return the result from server action
      return {
        success: true,
        studentId: result.studentId!,
        temporaryPassword: result.temporaryPassword!,
        message: result.message!
      }
    } catch (error) {
      set({
        error: handleError(error),
        loading: false
      })
      return null
    }
  },

  updateStudent: async (id: string, updates: Partial<StudentFormData>) => {
    set({ loading: true, error: null })
    try {
      // Rate limiting check
      if (!rateLimiter.isAllowed(getRateLimitId())) {
        throw new Error('Too many requests. Please wait before trying again.')
      }

      // Use the library function which handles all fields correctly
      const supabase = createClient()
      await updateStudentLib(supabase, id, updates)

      // Refresh the students list
      await get().fetchStudents()
      set({ loading: false })
    } catch (error) {
      set({
        error: handleError(error),
        loading: false
      })
    }
  },

  deleteStudent: async (id: string) => {
    set({ loading: true, error: null })
    try {
      // Rate limiting check
      if (!rateLimiter.isAllowed(getRateLimitId())) {
        throw new Error('Too many requests. Please wait before trying again.')
      }

      // Import the server action
      const { deleteStudent: deleteStudentAction } = await import('@/app/(admin)/students/actions')
      const result = await deleteStudentAction(id)

      if (result.error) {
        throw new Error(result.error)
      }

      // Refresh the students list
      await get().fetchStudents()
      set({ loading: false })
    } catch (error) {
      set({
        error: handleError(error),
        loading: false
      })
    }
  },

    clearError: () => set({ error: null })
  }
})
