import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { AdminConfigService } from '@/utils/adminConfigService';
import { useAuthStore } from '@/stores/authStore';

interface ConfigurationStatusProps {
  onConfigLoaded?: (isValid: boolean) => void;
}

export default function ConfigurationStatus({ onConfigLoaded }: ConfigurationStatusProps) {
  const { user } = useAuthStore();
  const [configStatus, setConfigStatus] = useState<{
    isLoading: boolean;
    isValid: boolean;
    issues: string[];
    geofenceCount: number;
    hasSettings: boolean;
  }>({
    isLoading: true,
    isValid: false,
    issues: [],
    geofenceCount: 0,
    hasSettings: false,
  });

  const adminConfigService = AdminConfigService.getInstance();

  useEffect(() => {
    checkConfiguration();
  }, [user]);

  const checkConfiguration = async () => {
    if (!user?.profile?.school_id) {
      setConfigStatus({
        isLoading: false,
        isValid: false,
        issues: ['School ID not found in user profile'],
        geofenceCount: 0,
        hasSettings: false,
      });
      onConfigLoaded?.(false);
      return;
    }

    try {
      setConfigStatus(prev => ({ ...prev, isLoading: true }));

      const config = await adminConfigService.getAdminConfig(user.profile.school_id);
      const validation = adminConfigService.validateSchoolConfig(config);

      const status = {
        isLoading: false,
        isValid: validation.isValid,
        issues: validation.issues,
        geofenceCount: config.geofences.length,
        hasSettings: !!config.settings,
      };

      setConfigStatus(status);
      onConfigLoaded?.(validation.isValid);

      // Log configuration status for debugging
      console.log('School configuration status:', {
        schoolId: user.profile.school_id,
        isValid: validation.isValid,
        geofences: config.geofences.length,
        hasSettings: !!config.settings,
        issues: validation.issues,
      });

    } catch (error: any) {
      console.error('Failed to check configuration:', error);
      const status = {
        isLoading: false,
        isValid: false,
        issues: [`Failed to load configuration: ${error.message}`],
        geofenceCount: 0,
        hasSettings: false,
      };
      setConfigStatus(status);
      onConfigLoaded?.(false);
    }
  };

  const handleRefreshConfig = async () => {
    if (!user?.profile?.school_id) return;

    try {
      // Force refresh configuration
      await adminConfigService.getAdminConfig(user.profile.school_id, true);
      await checkConfiguration();
      Alert.alert('Success', 'Configuration refreshed successfully!');
    } catch (error: any) {
      Alert.alert('Error', `Failed to refresh configuration: ${error.message}`);
    }
  };

  const getStatusColor = () => {
    if (configStatus.isLoading) return 'bg-gray-100';
    return configStatus.isValid ? 'bg-green-100' : 'bg-red-100';
  };

  const getStatusTextColor = () => {
    if (configStatus.isLoading) return 'text-gray-600';
    return configStatus.isValid ? 'text-green-800' : 'text-red-800';
  };

  const getStatusIcon = () => {
    if (configStatus.isLoading) return '⏳';
    return configStatus.isValid ? '✅' : '⚠️';
  };

  const getStatusMessage = () => {
    if (configStatus.isLoading) return 'Checking configuration...';
    if (configStatus.isValid) return 'School configuration is complete';
    return 'School configuration needs attention';
  };

  if (configStatus.isLoading) {
    return (
      <View className="bg-gray-100 rounded-xl p-4 mb-4">
        <View className="flex-row items-center">
          <Text className="text-gray-600 font-rubik">⏳ Checking school configuration...</Text>
        </View>
      </View>
    );
  }

  return (
    <View className={`${getStatusColor()} rounded-xl p-4 mb-4`}>
      <View className="flex-row justify-between items-start">
        <View className="flex-1">
          <View className="flex-row items-center mb-2">
            <Text className="text-lg mr-2">{getStatusIcon()}</Text>
            <Text className={`font-rubik-bold ${getStatusTextColor()}`}>
              {getStatusMessage()}
            </Text>
          </View>

          <View className="space-y-1">
            <Text className={`text-sm font-rubik ${getStatusTextColor()}`}>
              📍 Geofences: {configStatus.geofenceCount} configured
            </Text>
            <Text className={`text-sm font-rubik ${getStatusTextColor()}`}>
              ⚙️ Settings: {configStatus.hasSettings ? 'Configured' : 'Using defaults'}
            </Text>
          </View>

          {configStatus.issues.length > 0 && (
            <View className="mt-3">
              <Text className={`text-sm font-rubik-medium ${getStatusTextColor()} mb-1`}>
                Issues to resolve:
              </Text>
              {configStatus.issues.map((issue, index) => (
                <Text key={index} className={`text-xs font-rubik ${getStatusTextColor()}`}>
                  • {issue}
                </Text>
              ))}
            </View>
          )}
        </View>

        <TouchableOpacity
          onPress={handleRefreshConfig}
          className="bg-white/50 px-3 py-1 rounded-lg ml-3"
        >
          <Text className={`text-xs font-rubik-medium ${getStatusTextColor()}`}>
            Refresh
          </Text>
        </TouchableOpacity>
      </View>

      {!configStatus.isValid && (
        <View className="mt-3 pt-3 border-t border-red-200">
          <Text className="text-xs font-rubik text-red-700">
            💡 Contact your school administrator to configure attendance settings in the admin panel.
          </Text>
        </View>
      )}
    </View>
  );
}
