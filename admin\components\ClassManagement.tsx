'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useClassStore } from '@/stores/classStore'
import { useTeacherStore } from '@/stores/teacherStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Plus,
  Edit,
  Trash2,
  Users,
  BookOpen,
  GraduationCap,
  Calendar,
  AlertCircle,
  UserPlus,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ClassFormData {
  className: string
  grade: string
  section: string
  subject: string
  academicYear: string
}

interface ClassFormProps {
  formData: ClassFormData
  setFormData: React.Dispatch<React.SetStateAction<ClassFormData>>
  onSubmit: (e: React.FormEvent) => void
  onCancel: () => void
  loading: boolean
  editingClass: any
}

const ClassForm = React.memo(({
  formData,
  setFormData,
  onSubmit,
  onCancel,
  loading,
  editingClass
}: ClassFormProps) => {
  const gradeOptions = [
    'Pre-K', 'Kindergarten', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'
  ]

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="className">Class Name *</Label>
          <Input
            id="className"
            value={formData.className}
            onChange={(e) => setFormData(prev => ({ ...prev, className: e.target.value }))}
            placeholder="e.g., Math 101, English Literature"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="grade">Grade *</Label>
          <Select value={formData.grade} onValueChange={(value) => setFormData(prev => ({ ...prev, grade: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select grade" />
            </SelectTrigger>
            <SelectContent>
              {gradeOptions.map(grade => (
                <SelectItem key={grade} value={grade}>
                  Grade {grade}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="section">Section</Label>
          <Input
            id="section"
            value={formData.section}
            onChange={(e) => setFormData(prev => ({ ...prev, section: e.target.value }))}
            placeholder="e.g., A, B, C"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="subject">Subject</Label>
          <Input
            id="subject"
            value={formData.subject}
            onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
            placeholder="e.g., Mathematics, Science"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="academicYear">Academic Year *</Label>
        <Input
          id="academicYear"
          value={formData.academicYear}
          onChange={(e) => setFormData(prev => ({ ...prev, academicYear: e.target.value }))}
          placeholder="e.g., 2024-25"
          required
        />
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? 'Saving...' : editingClass ? 'Update Class' : 'Add Class'}
        </Button>
      </div>
    </form>
  )
})

export default function ClassManagement() {
  const {
    classes,
    loading,
    error,
    fetchClasses,
    addClass,
    updateClass,
    deleteClass,
    clearError
  } = useClassStore()

  const {
    teachers,
    fetchTeachers,
    assignTeacherToClass,
    removeTeacherFromClass
  } = useTeacherStore()

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isTeacherAssignDialogOpen, setIsTeacherAssignDialogOpen] = useState(false)
  const [editingClass, setEditingClass] = useState<any>(null)
  const [selectedClassForTeacher, setSelectedClassForTeacher] = useState<any>(null)
  const [formData, setFormData] = useState<ClassFormData>({
    className: '',
    grade: '',
    section: '',
    subject: '',
    academicYear: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1).toString().slice(-2)
  })

  useEffect(() => {
    fetchClasses()
    fetchTeachers()
  }, [fetchClasses, fetchTeachers])

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (editingClass) {
        await updateClass(editingClass.id, formData)
        setIsEditDialogOpen(false)
        setEditingClass(null)
      } else {
        await addClass(formData)
        setIsAddDialogOpen(false)
      }

      // Reset form
      setFormData({
        className: '',
        grade: '',
        section: '',
        subject: '',
        academicYear: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1).toString().slice(-2)
      })
    } catch (error) {
      console.error('Error submitting form:', error)
    }
  }, [editingClass, formData, updateClass, addClass])

  const handleEdit = useCallback((classItem: any) => {
    setEditingClass(classItem)
    setFormData({
      className: classItem.class_name,
      grade: classItem.grade,
      section: classItem.section || '',
      subject: classItem.subject || '',
      academicYear: classItem.academic_year
    })
    setIsEditDialogOpen(true)
  }, [])

  const handleDelete = useCallback(async (classId: string) => {
    if (window.confirm('Are you sure you want to delete this class? This action cannot be undone.')) {
      await deleteClass(classId)
    }
  }, [deleteClass])

  const resetForm = useCallback(() => {
    setFormData({
      className: '',
      grade: '',
      section: '',
      subject: '',
      academicYear: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1).toString().slice(-2)
    })
    setEditingClass(null)
  }, [])

  const handleCancel = useCallback(() => {
    resetForm()
    setIsAddDialogOpen(false)
    setIsEditDialogOpen(false)
  }, [resetForm])

  const handleAssignTeacher = useCallback((classItem: any) => {
    setSelectedClassForTeacher(classItem)
    setIsTeacherAssignDialogOpen(true)
  }, [])

  const handleTeacherAssignment = useCallback(async (teacherId: string, isPrimary: boolean = false) => {
    if (!selectedClassForTeacher) return

    try {
      await assignTeacherToClass(teacherId, selectedClassForTeacher.id, isPrimary)
      await fetchClasses() // Refresh to show updated assignments
    } catch (error) {
      console.error('Error assigning teacher:', error)
    }
  }, [selectedClassForTeacher, assignTeacherToClass, fetchClasses])

  const handleRemoveTeacher = useCallback(async (teacherId: string, classId: string) => {
    if (window.confirm('Are you sure you want to remove this teacher from the class?')) {
      try {
        await removeTeacherFromClass(teacherId, classId)
        await fetchClasses() // Refresh to show updated assignments
      } catch (error) {
        console.error('Error removing teacher:', error)
      }
    }
  }, [removeTeacherFromClass, fetchClasses])

  if (loading && classes.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading classes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-destructive/15 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-destructive">{error}</p>
            <Button variant="ghost" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </div>
        </div>
      )}

      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Class Management
              </CardTitle>
              <CardDescription>
                Create and manage classes for your school
              </CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Class
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Add New Class</DialogTitle>
                  <DialogDescription>
                    Create a new class for students and teachers
                  </DialogDescription>
                </DialogHeader>
                <ClassForm
                  formData={formData}
                  setFormData={setFormData}
                  onSubmit={handleSubmit}
                  onCancel={handleCancel}
                  loading={loading}
                  editingClass={editingClass}
                />
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* Classes Grid */}
      {classes.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center text-muted-foreground">
              <BookOpen className="h-8 w-8 mx-auto mb-2" />
              <p>No classes found</p>
              <p className="text-sm">Create your first class to get started</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {classes.map(classItem => (
            <Card key={classItem.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{classItem.class_name}</CardTitle>
                    <CardDescription className="flex items-center gap-2 mt-1">
                      <GraduationCap className="h-4 w-4" />
                      Grade {classItem.grade}
                      {classItem.section && ` - Section ${classItem.section}`}
                    </CardDescription>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleAssignTeacher(classItem)}
                      title="Assign Teachers"
                    >
                      <UserPlus className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(classItem)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(classItem.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {classItem.subject && (
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{classItem.subject}</span>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{classItem.academic_year}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{classItem.studentCount || 0} students</span>
                </div>

                {classItem.teachers && classItem.teachers.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground">Assigned Teachers:</div>
                    <div className="flex flex-wrap gap-1">
                      {classItem.teachers.map(teacher => (
                        <Badge key={teacher.id} variant="secondary" className="text-xs flex items-center gap-1">
                          {teacher.name}
                          <button
                            onClick={() => handleRemoveTeacher(teacher.id, classItem.id)}
                            className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                            title="Remove teacher"
                          >
                            <X className="h-2 w-2" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Class</DialogTitle>
            <DialogDescription>
              Update class information
            </DialogDescription>
          </DialogHeader>
          <ClassForm
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={loading}
            editingClass={editingClass}
          />
        </DialogContent>
      </Dialog>

      {/* Teacher Assignment Dialog */}
      <Dialog open={isTeacherAssignDialogOpen} onOpenChange={setIsTeacherAssignDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Assign Teachers</DialogTitle>
            <DialogDescription>
              Assign teachers to {selectedClassForTeacher?.class_name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {teachers.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">
                No teachers available. Please add teachers first.
              </p>
            ) : (
              <div className="space-y-3">
                {teachers
                  .filter(teacher =>
                    !selectedClassForTeacher?.teachers?.some((t: any) => t.id === teacher.id)
                  )
                  .map(teacher => (
                    <div key={teacher.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <div className="font-medium">{teacher.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {teacher.teacher_id} • {teacher.email}
                        </div>
                        {teacher.subject && (
                          <div className="text-sm text-muted-foreground">
                            Subject: {teacher.subject}
                          </div>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTeacherAssignment(teacher.id, false)}
                        >
                          Assign
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleTeacherAssignment(teacher.id, true)}
                        >
                          Assign as Primary
                        </Button>
                      </div>
                    </div>
                  ))}
                {teachers.filter(teacher =>
                  !selectedClassForTeacher?.teachers?.some((t: any) => t.id === teacher.id)
                ).length === 0 && (
                  <p className="text-muted-foreground text-center py-8">
                    All available teachers are already assigned to this class.
                  </p>
                )}
              </div>
            )}
            <div className="flex justify-end pt-4">
              <Button
                variant="outline"
                onClick={() => setIsTeacherAssignDialogOpen(false)}
              >
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
