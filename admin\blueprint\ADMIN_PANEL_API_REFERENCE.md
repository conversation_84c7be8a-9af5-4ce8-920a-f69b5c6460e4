# Admin Panel API Reference for Teacher Attendance System

## Overview

This document provides the database schema and API endpoints that the **web admin panel** needs to implement for configuring the teacher attendance system. The mobile app will fetch these configurations to enable location-based attendance tracking.

## Database Tables for Admin Configuration

### 1. **school_geofences** - Location Boundaries

This table stores the geographic boundaries where teachers can mark attendance.

```sql
CREATE TABLE school_geofences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    school_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,                    -- e.g., "Main Campus", "Building A"
    center_latitude DECIMAL(10, 8) NOT NULL,       -- GPS latitude
    center_longitude DECIMAL(11, 8) NOT NULL,      -- GPS longitude
    radius_meters INTEGER NOT NULL,                -- Allowed distance from center
    is_active BOOLEAN DEFAULT TRUE,                -- Enable/disable this geofence
    
    -- Time restrictions
    allowed_check_in_start TIME,                   -- e.g., '07:00:00'
    allowed_check_in_end TIME,                     -- e.g., '09:00:00'
    allowed_check_out_start TIME,                  -- e.g., '15:00:00'
    allowed_check_out_end TIME,                    -- e.g., '18:00:00'
    
    -- Days of week (0 = Sunday, 6 = Saturday)
    allowed_days INTEGER[] DEFAULT '{1,2,3,4,5}',  -- Monday to Friday
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES auth.users(id)
);
```

### 2. **teacher_attendance_settings** - Security Configuration

This table stores security and attendance policy settings.

```sql
CREATE TABLE teacher_attendance_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    school_id UUID NOT NULL UNIQUE,
    
    -- Time restrictions
    earliest_check_in TIME DEFAULT '06:00:00',
    latest_check_in TIME DEFAULT '10:00:00',
    earliest_check_out TIME DEFAULT '14:00:00',
    latest_check_out TIME DEFAULT '20:00:00',
    
    -- Location settings
    require_location BOOLEAN DEFAULT TRUE,
    max_location_accuracy_meters INTEGER DEFAULT 50,
    geofence_buffer_meters INTEGER DEFAULT 100,
    
    -- Security settings
    max_daily_attempts INTEGER DEFAULT 5,
    lockout_duration_minutes INTEGER DEFAULT 30,
    require_photo_verification BOOLEAN DEFAULT FALSE,
    allow_manual_override BOOLEAN DEFAULT TRUE,
    
    -- Notification settings
    notify_admin_on_suspicious BOOLEAN DEFAULT TRUE,
    notify_teacher_on_location_fail BOOLEAN DEFAULT TRUE,
    
    -- Work hours calculation
    minimum_work_hours DECIMAL(4, 2) DEFAULT 8.0,
    break_time_minutes INTEGER DEFAULT 60,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID REFERENCES auth.users(id)
);
```

## Admin Panel Features to Implement

### 🏫 **1. Geofence Management**

**Purpose**: Configure where teachers can mark attendance

**Features Needed**:
- ✅ **Map Interface**: Interactive map to set school location
- ✅ **Current Location**: Button to use admin's current location
- ✅ **Multiple Locations**: Support for multiple buildings/campuses
- ✅ **Radius Configuration**: Adjustable geofence radius (50-500 meters)
- ✅ **Time Windows**: Set allowed check-in/check-out times
- ✅ **Active/Inactive**: Enable/disable specific geofences

**Sample Admin Interface**:
```
📍 School Location Configuration
┌─────────────────────────────────────┐
│ [Interactive Map]                   │
│ 📍 Current: Main Campus             │
│ Lat: 40.7128, Lng: -74.0060        │
│ Radius: [100] meters                │
│                                     │
│ ⏰ Check-in: [07:00] - [09:00]      │
│ ⏰ Check-out: [15:00] - [18:00]     │
│                                     │
│ [Use Current Location] [Save]       │
└─────────────────────────────────────┘
```

### ⚙️ **2. Attendance Settings**

**Purpose**: Configure security and policy settings

**Settings to Include**:
- **Time Restrictions**: Global check-in/out windows
- **Location Accuracy**: Required GPS accuracy (meters)
- **Security Limits**: Max daily attempts, lockout duration
- **Notifications**: Admin alerts for suspicious activity
- **Work Hours**: Minimum required work hours

### 📊 **3. Monitoring Dashboard**

**Purpose**: View attendance data and security events

**Features**:
- **Real-time Attendance**: Who's checked in/out today
- **Security Alerts**: Failed location verifications
- **Attendance Reports**: Daily/weekly/monthly reports
- **Geofence Analytics**: Usage statistics per location

## API Endpoints for Mobile App

The mobile app needs these endpoints to function:

### **GET /api/geofences/{school_id}**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Main Campus",
      "center_latitude": 40.7128,
      "center_longitude": -74.0060,
      "radius_meters": 100,
      "allowed_check_in_start": "07:00:00",
      "allowed_check_in_end": "09:00:00",
      "allowed_check_out_start": "15:00:00",
      "allowed_check_out_end": "18:00:00",
      "allowed_days": [1, 2, 3, 4, 5],
      "is_active": true
    }
  ]
}
```

### **GET /api/attendance-settings/{school_id}**
```json
{
  "success": true,
  "data": {
    "earliest_check_in": "06:00:00",
    "latest_check_in": "10:00:00",
    "earliest_check_out": "14:00:00",
    "latest_check_out": "20:00:00",
    "require_location": true,
    "max_location_accuracy_meters": 50,
    "max_daily_attempts": 5,
    "lockout_duration_minutes": 30
  }
}
```

## Sample Data for Testing

### Insert Sample Geofence
```sql
INSERT INTO school_geofences (
    school_id, 
    name, 
    center_latitude, 
    center_longitude, 
    radius_meters,
    allowed_check_in_start,
    allowed_check_in_end,
    allowed_check_out_start,
    allowed_check_out_end
) VALUES (
    'your-school-id',
    'Main Campus',
    40.7128,        -- Replace with your school's latitude
    -74.0060,       -- Replace with your school's longitude
    100,            -- 100 meter radius
    '07:00:00',
    '09:00:00',
    '15:00:00',
    '18:00:00'
);
```

### Insert Sample Settings
```sql
INSERT INTO teacher_attendance_settings (school_id)
VALUES ('your-school-id');
```

## Mobile App Integration

The mobile app automatically:
1. **Fetches geofences** when teacher opens attendance
2. **Validates location** against admin-configured boundaries
3. **Enforces time windows** set by admin
4. **Applies security settings** configured by admin
5. **Logs security events** for admin review

## Admin Panel Workflow

1. **Admin logs into web panel**
2. **Sets school location** on interactive map
3. **Configures time windows** for attendance
4. **Adjusts security settings** as needed
5. **Mobile app automatically uses** new settings

## Security Considerations

- ✅ **Admin Authentication**: Only authorized admins can modify settings
- ✅ **Audit Logging**: All admin changes are logged
- ✅ **Validation**: Coordinates and times are validated
- ✅ **Backup**: Settings are backed up before changes
- ✅ **Real-time Updates**: Mobile apps get updates immediately

## Testing Checklist

For the web admin panel:
- [ ] Can set school location on map
- [ ] Can adjust geofence radius
- [ ] Can set time windows
- [ ] Can enable/disable geofences
- [ ] Can configure security settings
- [ ] Changes reflect in mobile app immediately

For the mobile app:
- [ ] Fetches latest geofence data
- [ ] Validates location correctly
- [ ] Enforces time restrictions
- [ ] Shows appropriate error messages
- [ ] Logs security events

## Support

The mobile app is ready to work with any admin panel that:
1. **Manages the database tables** described above
2. **Provides the API endpoints** for configuration
3. **Follows the data formats** specified

The mobile app will automatically adapt to any changes made through the admin panel without requiring app updates.
